services:
  postgres-test:
    image: postgres:17.3-alpine
    environment:
      POSTGRES_DB: authentication_db
      POSTGRES_USER: auth_user
      POSTGRES_PASSWORD: auth_password
    ports:
      - "54320:5432"
    volumes:
      - authdb:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U test_user -d test_auth_db"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  authdb:
    name: authdb
# GEDSYS Authentication Service - API Documentation

## Overview

This document provides comprehensive information about all available REST API endpoints in the GEDSYS Authentication Service. The service provides JWT-based authentication, user management, and push token management capabilities.

## Security Configuration

The service implements a comprehensive security configuration with the following features:

### JWT Service Testing
The JWT service includes extensive test coverage ensuring reliability and security:
- **Token Generation**: Validates proper token creation for different session types (mobile/web)
- **Token Validation**: Tests signature verification, expiration checks, and user validation
- **Claim Extraction**: Verifies extraction of username, user ID, role, and session type
- **Edge Case Handling**: Tests malformed tokens, tampered signatures, and null values
- **Session Type Awareness**: Validates different expiration times for mobile vs web sessions
- **Error Handling**: Ensures proper exception handling for security violations

### JWT Authentication Filter
- Custom JWT authentication filter processes all requests
- Extracts and validates JWT tokens from Authorization header
- Sets security context for authenticated requests

### CORS Configuration
- Configurable cross-origin resource sharing
- Supports localhost development environments
- Allows credentials and exposes necessary headers
- Caches preflight responses for performance

### Endpoint Security
- Public endpoints: `/auth/login`, `/auth/refresh`, health checks
- Protected endpoints: All `/auth/**` endpoints require authentication
- Admin endpoints: All `/admin/**` endpoints require ADMIN role
- Method-level security with `@PreAuthorize` annotations

### Session Management
- Stateless session creation policy
- Custom authentication entry point for 401 errors
- Automatic session invalidation on security events

## Base URL
```
http://localhost:8080
```

## Authentication
Most endpoints require JWT authentication via the `Authorization` header:
```
Authorization: Bearer <jwt-token>
```

## Response Format
All responses follow a consistent JSON format:

### Success Response
```json
{
  "data": { ... },
  "message": "Operation successful"
}
```

### Error Response
```json
{
  "error": "Error description",
  "timestamp": "2024-01-01T12:00:00Z",
  "path": "/api/endpoint"
}
```

## Endpoints

### 🔐 Authentication Endpoints

#### POST /auth/login
Authenticate user and create session.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "sessionType": "MOBILE|WEB",
  "pushToken": "fcm-token-here",      // Optional, mobile only
  "deviceType": "IOS|ANDROID",        // Optional, mobile only
  "deviceId": "device-uuid"           // Optional, mobile only
}
```

**Response:**
```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIs...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
  "tokenType": "Bearer",
  "expiresIn": 3600,
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "role": "USER",
    "enabled": true,
    "createdAt": "2024-01-01T12:00:00Z"
  }
}
```

**Status Codes:**
- `200 OK` - Login successful
- `401 Unauthorized` - Invalid credentials
- `403 Forbidden` - Account disabled
- `400 Bad Request` - Invalid request format

---

#### POST /auth/refresh
Refresh access token using refresh token.

**Request Body:**
```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIs..."
}
```

**Response:**
```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIs...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
  "tokenType": "Bearer",
  "expiresIn": 3600
}
```

**Status Codes:**
- `200 OK` - Token refreshed successfully
- `401 Unauthorized` - Invalid or expired refresh token
- `400 Bad Request` - Invalid request format

---

#### POST /auth/logout
Logout user and invalidate tokens.

**Headers:**
```
Authorization: Bearer <jwt-token>
```

**Request Body:**
```json
{
  "sessionType": "MOBILE|WEB"  // Optional
}
```

**Response:**
```json
{
  "message": "Logout successful"
}
```

**Status Codes:**
- `200 OK` - Logout successful
- `401 Unauthorized` - Invalid token
- `400 Bad Request` - Invalid session type

---

#### GET /auth/profile
Get current user profile.

**Headers:**
```
Authorization: Bearer <jwt-token>
```

**Response:**
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "role": "USER",
  "enabled": true,
  "createdAt": "2024-01-01T12:00:00Z",
  "updatedAt": "2024-01-01T12:00:00Z"
}
```

**Status Codes:**
- `200 OK` - Profile retrieved successfully
- `401 Unauthorized` - Invalid token

---

#### PUT /auth/change-password
Change user password.

**Headers:**
```
Authorization: Bearer <jwt-token>
```

**Request Body:**
```json
{
  "currentPassword": "oldPassword123",
  "newPassword": "newPassword456",
  "confirmPassword": "newPassword456"
}
```

**Response:**
```json
{
  "message": "Password changed successfully. Please login again."
}
```

**Status Codes:**
- `200 OK` - Password changed successfully
- `400 Bad Request` - Password validation failed
- `401 Unauthorized` - Invalid current password
- `422 Unprocessable Entity` - Password confirmation mismatch

---

#### GET /auth/sessions
Get active sessions information.

**Headers:**
```
Authorization: Bearer <jwt-token>
```

**Response:**
```json
{
  "hasMobileSession": true,
  "hasWebSession": false,
  "totalActiveSessions": 1
}
```

**Status Codes:**
- `200 OK` - Session info retrieved successfully
- `401 Unauthorized` - Invalid token

---

#### GET /auth/validate
Validate current JWT token.

**Headers:**
```
Authorization: Bearer <jwt-token>
```

**Response:**
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "role": "USER",
  "enabled": true,
  "createdAt": "2024-01-01T12:00:00Z"
}
```

**Status Codes:**
- `200 OK` - Token is valid
- `401 Unauthorized` - Invalid or expired token

### 📱 Push Token Endpoints

#### POST /auth/push-token
Register or update push token (Mobile sessions only).

**Headers:**
```
Authorization: Bearer <mobile-jwt-token>
```

**Request Body:**
```json
{
  "pushToken": "fcm-token-here",
  "deviceType": "IOS|ANDROID",
  "deviceId": "device-uuid"
}
```

**Response:**
```json
{
  "message": "Push token registered successfully",
  "pushToken": {
    "id": 1,
    "pushToken": "fcm-token-here",
    "deviceType": "IOS",
    "deviceId": "device-uuid",
    "createdAt": "2024-01-01T12:00:00Z",
    "lastUsedAt": "2024-01-01T12:00:00Z"
  }
}
```

**Status Codes:**
- `200 OK` - Push token registered successfully
- `403 Forbidden` - Not a mobile session
- `400 Bad Request` - Invalid device type or token

---

#### GET /auth/push-token
Get current push token (Mobile sessions only).

**Headers:**
```
Authorization: Bearer <mobile-jwt-token>
```

**Response:**
```json
{
  "message": "Push token retrieved successfully",
  "pushToken": {
    "id": 1,
    "pushToken": "fcm-token-here",
    "deviceType": "IOS",
    "deviceId": "device-uuid",
    "createdAt": "2024-01-01T12:00:00Z",
    "lastUsedAt": "2024-01-01T12:00:00Z"
  }
}
```

**Status Codes:**
- `200 OK` - Push token retrieved successfully
- `403 Forbidden` - Not a mobile session
- `404 Not Found` - No push token registered

---

#### PUT /auth/push-token
Update existing push token (Mobile sessions only).

**Headers:**
```
Authorization: Bearer <mobile-jwt-token>
```

**Request Body:**
```json
{
  "pushToken": "new-fcm-token-here",
  "deviceType": "ANDROID",
  "deviceId": "new-device-uuid"
}
```

**Response:**
```json
{
  "message": "Push token updated successfully",
  "pushToken": {
    "id": 1,
    "pushToken": "new-fcm-token-here",
    "deviceType": "ANDROID",
    "deviceId": "new-device-uuid",
    "createdAt": "2024-01-01T12:00:00Z",
    "lastUsedAt": "2024-01-01T13:00:00Z"
  }
}
```

**Status Codes:**
- `200 OK` - Push token updated successfully
- `403 Forbidden` - Not a mobile session
- `400 Bad Request` - No existing token to update

---

#### DELETE /auth/push-token
Delete push token (Mobile sessions only).

**Headers:**
```
Authorization: Bearer <mobile-jwt-token>
```

**Response:**
```json
{
  "message": "Push token deleted successfully"
}
```

**Status Codes:**
- `200 OK` - Push token deleted successfully
- `403 Forbidden` - Not a mobile session

---

#### GET /auth/push-token/exists
Check if user has push token (Mobile sessions only).

**Headers:**
```
Authorization: Bearer <mobile-jwt-token>
```

**Response:**
```json
{
  "hasPushToken": true,
  "message": "User has push token"
}
```

**Status Codes:**
- `200 OK` - Check completed successfully
- `403 Forbidden` - Not a mobile session

### 👑 Admin Endpoints

All admin endpoints require `ADMIN` role.

#### GET /admin/users
Get paginated list of users with optional filters.

**Headers:**
```
Authorization: Bearer <admin-jwt-token>
```

**Query Parameters:**
- `email` (optional) - Filter by email (partial match)
- `enabled` (optional) - Filter by enabled status (true/false)
- `role` (optional) - Filter by role (USER/ADMIN)
- `page` (optional, default: 0) - Page number
- `size` (optional, default: 20) - Page size (max: 100)
- `sort` (optional, default: "createdAt,desc") - Sort criteria

**Example:**
```
GET /admin/users?email=john&enabled=true&role=USER&page=0&size=10&sort=email,asc
```

**Response:**
```json
{
  "content": [
    {
      "id": 1,
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "role": "USER",
      "enabled": true,
      "createdAt": "2024-01-01T12:00:00Z",
      "updatedAt": "2024-01-01T12:00:00Z"
    }
  ],
  "pageable": {
    "page": 0,
    "size": 10,
    "sort": "email,asc"
  },
  "totalElements": 1,
  "totalPages": 1,
  "first": true,
  "last": true,
  "empty": false
}
```

**Status Codes:**
- `200 OK` - Users retrieved successfully
- `403 Forbidden` - Not an admin user
- `400 Bad Request` - Invalid pagination parameters

---

#### GET /admin/users/{userId}
Get detailed user information by ID.

**Headers:**
```
Authorization: Bearer <admin-jwt-token>
```

**Response:**
```json
{
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "role": "USER",
    "enabled": true,
    "createdAt": "2024-01-01T12:00:00Z",
    "updatedAt": "2024-01-01T12:00:00Z"
  },
  "activeSessions": [
    {
      "sessionType": "MOBILE",
      "lastActivity": "2024-01-01T12:00:00Z",
      "expiresAt": "2024-01-08T12:00:00Z"
    }
  ],
  "pushToken": {
    "id": 1,
    "deviceType": "IOS",
    "deviceId": "device-uuid",
    "createdAt": "2024-01-01T12:00:00Z",
    "lastUsedAt": "2024-01-01T12:00:00Z"
  }
}
```

**Status Codes:**
- `200 OK` - User details retrieved successfully
- `403 Forbidden` - Not an admin user
- `404 Not Found` - User not found

---

#### POST /admin/users
Create a new user.

**Headers:**
```
Authorization: Bearer <admin-jwt-token>
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "Jane",
  "lastName": "Smith",
  "role": "USER",
  "enabled": true
}
```

**Response:**
```json
{
  "id": 2,
  "email": "<EMAIL>",
  "firstName": "Jane",
  "lastName": "Smith",
  "role": "USER",
  "enabled": true,
  "createdAt": "2024-01-01T12:00:00Z",
  "updatedAt": "2024-01-01T12:00:00Z"
}
```

**Status Codes:**
- `201 Created` - User created successfully
- `403 Forbidden` - Not an admin user
- `409 Conflict` - Email already exists
- `400 Bad Request` - Invalid user data

---

#### PUT /admin/users/{userId}
Update an existing user.

**Headers:**
```
Authorization: Bearer <admin-jwt-token>
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "newPassword123",  // Optional
  "firstName": "Jane",
  "lastName": "Doe",
  "role": "ADMIN",
  "enabled": false
}
```

**Response:**
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "firstName": "Jane",
  "lastName": "Doe",
  "role": "ADMIN",
  "enabled": false,
  "createdAt": "2024-01-01T12:00:00Z",
  "updatedAt": "2024-01-01T13:00:00Z"
}
```

**Status Codes:**
- `200 OK` - User updated successfully
- `403 Forbidden` - Not an admin user
- `404 Not Found` - User not found
- `409 Conflict` - Email already exists
- `400 Bad Request` - Invalid user data

---

#### DELETE /admin/users/{userId}
Delete a user.

**Headers:**
```
Authorization: Bearer <admin-jwt-token>
```

**Response:**
```json
{
  "message": "User deleted successfully"
}
```

**Status Codes:**
- `200 OK` - User deleted successfully
- `403 Forbidden` - Not an admin user
- `404 Not Found` - User not found

---

#### PATCH /admin/users/{userId}/enabled
Enable or disable a user.

**Headers:**
```
Authorization: Bearer <admin-jwt-token>
```

**Request Body:**
```json
{
  "enabled": false
}
```

**Response:**
```json
{
  "message": "User disabled successfully",
  "enabled": false
}
```

**Status Codes:**
- `200 OK` - User status changed successfully
- `403 Forbidden` - Not an admin user
- `404 Not Found` - User not found
- `400 Bad Request` - Invalid enabled status

---

#### POST /admin/users/{userId}/invalidate-sessions
Invalidate all sessions for a user.

**Headers:**
```
Authorization: Bearer <admin-jwt-token>
```

**Response:**
```json
{
  "message": "All user sessions invalidated successfully"
}
```

**Status Codes:**
- `200 OK` - Sessions invalidated successfully
- `403 Forbidden` - Not an admin user
- `404 Not Found` - User not found

---

#### GET /admin/users/stats
Get user statistics.

**Headers:**
```
Authorization: Bearer <admin-jwt-token>
```

**Response:**
```json
{
  "totalUsers": 150,
  "enabledUsers": 140,
  "disabledUsers": 10,
  "adminUsers": 5,
  "regularUsers": 145,
  "totalPushTokens": 85
}
```

**Status Codes:**
- `200 OK` - Statistics retrieved successfully
- `403 Forbidden` - Not an admin user

## Exception Handling

The service implements a comprehensive global exception handling system that provides consistent error responses across all endpoints.

### Exception Classes

The following custom exceptions are defined for domain-specific error handling:

- **`InvalidCredentialsException`** - Thrown when login credentials are invalid
- **`TokenExpiredException`** - Thrown when JWT or refresh tokens have expired
- **`InvalidTokenException`** - Thrown when tokens are malformed or invalid
- **`UserNotFoundException`** - Thrown when a user cannot be found
- **`EmailAlreadyExistsException`** - Thrown when attempting to create a user with an existing email

### Error Response Format

All errors return a consistent JSON structure:

```json
{
  "error": "ERROR_CODE",
  "message": "Human-readable error description",
  "status": 400,
  "timestamp": "2024-01-01T12:00:00",
  "path": "/api/endpoint",
  "validationErrors": {
    "field": "error message"
  }
}
```

### Security Event Logging

The system logs security-related events for monitoring and audit purposes:
- Authentication failures with client IP addresses
- Token expiration events
- Access denied attempts
- Invalid token usage
- User creation attempts with existing emails

## Error Codes

### Common HTTP Status Codes
- `200 OK` - Request successful
- `201 Created` - Resource created successfully
- `400 Bad Request` - Invalid request format or parameters
- `401 Unauthorized` - Authentication required or invalid
- `403 Forbidden` - Access denied (insufficient permissions)
- `404 Not Found` - Resource not found
- `409 Conflict` - Resource conflict (e.g., email already exists)
- `422 Unprocessable Entity` - Validation errors
- `500 Internal Server Error` - Server error

### Custom Error Messages
- `INVALID_CREDENTIALS` - Invalid email or password
- `ACCOUNT_DISABLED` - User account is disabled
- `TOKEN_EXPIRED` - JWT token or refresh token has expired
- `INVALID_TOKEN` - JWT token is invalid or malformed
- `EMAIL_ALREADY_EXISTS` - Email address is already registered
- `USER_NOT_FOUND` - User does not exist
- `INVALID_SESSION_TYPE` - Invalid session type provided
- `MOBILE_SESSION_REQUIRED` - Operation requires mobile session
- `ADMIN_ROLE_REQUIRED` - Operation requires admin privileges


## Rate Limiting

Currently, no rate limiting is implemented. Consider implementing rate limiting for production use:
- Login attempts: 5 per minute per IP
- Token refresh: 10 per minute per user
- Admin operations: 100 per minute per admin user

## Versioning

The API currently does not use versioning. Future versions should consider:
- URL versioning: `/v1/auth/login`
- Header versioning: `Accept: application/vnd.gedsys.v1+json`
- Parameter versioning: `/auth/login?version=1`
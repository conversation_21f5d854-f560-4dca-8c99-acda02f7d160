# GEDSYS Authentication Service

A comprehensive JWT-based authentication and authorization microservice built with Spring Boot 3.5.4 and Java 21. This service provides secure user authentication, session management, and role-based access control for the GEDSYS platform.

## 🚀 Features

### Core Authentication
- **JWT-based Authentication**: Stateless authentication using JSON Web Tokens
- **Multi-Session Support**: Separate session types for mobile and web clients
- **Refresh Token Rotation**: Single-use refresh tokens with automatic rotation
- **Role-Based Access Control**: USER and ADMIN roles with method-level security
- **Session Management**: Active session tracking and lifecycle management

### User Management
- **User Registration & Profiles**: Complete user lifecycle management
- **Password Security**: Secure password hashing and validation
- **Admin Operations**: Full CRUD operations with pagination and filtering
- **Account Management**: Enable/disable users and session invalidation

### Mobile Integration
- **Push Token Management**: FCM/APNS token registration and management
- **Device-Specific Sessions**: Different token expiration policies for mobile/web
- **Session Type Awareness**: API endpoints adapt behavior based on session type

### Security Features
- **CORS Configuration**: Configurable cross-origin resource sharing
- **Custom Authentication Entry Points**: Proper error handling for 401/403 responses
- **Method-Level Security**: Fine-grained authorization with `@PreAuthorize`
- **Session Invalidation**: Automatic cleanup on password changes and logout

## 🏗️ Architecture

### Technology Stack
- **Framework**: Spring Boot 3.5.4
- **Language**: Java 21
- **Database**: PostgreSQL 17
- **Security**: Spring Security 6
- **JWT**: JJWT 0.12.6
- **Migration**: Flyway
- **Testing**: Testcontainers, H2
- **Build**: Maven

### Project Structure
```
src/main/java/co/com/gedsys/authentication/
├── AuthenticationApplication.java          # Main application
├── config/                                 # Security & JWT configuration
│   ├── CustomAuthenticationEntryPoint.java
│   ├── JwtAuthenticationFilter.java
│   ├── JwtProperties.java
│   ├── SecurityBeanConfig.java
│   └── SecurityConfiguration.java
├── controller/                             # REST endpoints
│   ├── AdminController.java                # Admin user management
│   ├── AuthenticationController.java       # Auth operations
│   └── PushTokenController.java            # Push token management
├── dto/                                    # Data transfer objects
├── entity/                                 # JPA entities
├── exception/                              # Custom exceptions
├── repository/                             # Data access layer
└── service/                                # Business logic
```

## 🔧 Configuration

### Environment Variables
```bash
# Database
DB_USERNAME=auth_user
DB_PASSWORD=auth_password

# JWT Configuration
JWT_SECRET=your-secret-key-here
JWT_ACCESS_EXPIRATION=3600000          # 1 hour
JWT_REFRESH_EXPIRATION=604800000       # 7 days
JWT_MOBILE_ACCESS_EXPIRATION=7200000   # 2 hours (mobile)
JWT_WEB_ACCESS_EXPIRATION=3600000      # 1 hour (web)
```

### Application Profiles
- **default**: Production configuration
- **dev**: Development with Docker Compose integration
- **test**: Testing configuration with H2 database

## 🚦 Getting Started

### Prerequisites
- Java 21+
- Maven 3.6+
- Docker & Docker Compose
- PostgreSQL 17 (or use Docker Compose)

### Quick Start
1. **Clone and setup**:
   ```bash
   git clone <repository-url>
   cd gedsys-authentication
   ```

2. **Start database** (development):
   ```bash
   docker-compose up -d
   ```

3. **Run the application**:
   ```bash
   ./mvnw spring-boot:run -Dspring-boot.run.profiles=dev
   ```

4. **Run tests**:
   ```bash
   ./mvnw test
   ```

### Database Setup
The application uses Flyway for database migrations. Migrations are automatically applied on startup.

**Migration files**:
- `V1__Create_users_table.sql` - User entity and roles
- `V2__Create_refresh_tokens_table.sql` - Refresh token management
- `V3__Create_push_tokens_table.sql` - Push notification tokens

## 📡 API Documentation

### Authentication Endpoints

#### Login
```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "sessionType": "MOBILE",
  "pushToken": "fcm-token-here",
  "deviceType": "IOS",
  "deviceId": "device-uuid"
}
```

#### Refresh Token
```http
POST /auth/refresh
Content-Type: application/json

{
  "refreshToken": "refresh-token-here"
}
```

#### Logout
```http
POST /auth/logout
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "sessionType": "MOBILE"
}
```

#### User Profile
```http
GET /auth/profile
Authorization: Bearer <jwt-token>
```

#### Change Password
```http
PUT /auth/change-password
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "currentPassword": "oldPassword",
  "newPassword": "newPassword123",
  "confirmPassword": "newPassword123"
}
```

### Admin Endpoints (Requires ADMIN role)

#### List Users
```http
GET /admin/users?page=0&size=20&sort=createdAt,desc&email=user&enabled=true&role=USER
Authorization: Bearer <admin-jwt-token>
```

#### Get User Details
```http
GET /admin/users/{userId}
Authorization: Bearer <admin-jwt-token>
```

#### Create User
```http
POST /admin/users
Authorization: Bearer <admin-jwt-token>
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "John",
  "lastName": "Doe",
  "role": "USER",
  "enabled": true
}
```

#### Update User
```http
PUT /admin/users/{userId}
Authorization: Bearer <admin-jwt-token>
Content-Type: application/json

{
  "email": "<EMAIL>",
  "firstName": "Jane",
  "lastName": "Smith",
  "role": "USER",
  "enabled": true
}
```

### Push Token Endpoints (Mobile sessions only)

#### Register Push Token
```http
POST /auth/push-token
Authorization: Bearer <mobile-jwt-token>
Content-Type: application/json

{
  "pushToken": "fcm-token-here",
  "deviceType": "ANDROID",
  "deviceId": "device-uuid"
}
```

#### Get Push Token
```http
GET /auth/push-token
Authorization: Bearer <mobile-jwt-token>
```

## 🔒 Security Configuration

### JWT Configuration
The service uses JWT tokens with the following characteristics:
- **Access Tokens**: Short-lived (1-2 hours) for API access
- **Refresh Tokens**: Longer-lived (7 days) for token renewal
- **Single-Use Policy**: Refresh tokens are invalidated after use
- **Session-Aware**: Different expiration times for mobile vs web

#### JWT Testing
The JWT service includes comprehensive test coverage for:
- **Token Generation**: Valid token creation for different session types and users
- **Token Validation**: Signature verification, expiration checks, and user validation
- **Claim Extraction**: Username, user ID, role, session type, and expiration data
- **Edge Cases**: Malformed tokens, tampered signatures, null values, and expired tokens
- **Session Types**: Different expiration times for mobile (2 hours) vs web (1 hour) sessions
- **Error Handling**: Proper exception handling for invalid tokens and security violations

### CORS Configuration
```java
// Allowed origins
"http://localhost:*", "https://localhost:*"

// Allowed methods
GET, POST, PUT, DELETE, PATCH, OPTIONS

// Allowed headers
Authorization, Content-Type, X-Requested-With, Accept, Origin
```

### Role-Based Access Control
- **USER**: Standard user operations (profile, password change)
- **ADMIN**: Full user management capabilities
- **Method-Level Security**: `@PreAuthorize` annotations on sensitive operations

## 🧪 Testing

### Test Structure
```
src/test/java/co/com/gedsys/authentication/
├── AuthenticationApplicationTests.java     # Application context tests
├── BaseIntegrationTest.java               # Base integration test setup
├── DatabaseIntegrationTest.java           # Database-specific tests
├── config/
│   └── TestDatabaseConfig.java            # Test database configuration
├── repository/                            # Repository layer tests
│   ├── UserRepositoryTest.java
│   ├── RefreshTokenRepositoryTest.java
│   └── PushTokenRepositoryTest.java
└── service/                               # Service layer tests
    ├── JwtServiceTest.java                # Comprehensive JWT functionality tests
    ├── AuthenticationServiceTest.java
    ├── UserServiceTest.java
    ├── RefreshTokenServiceTest.java
    └── PushTokenServiceTest.java
```

### Running Tests
```bash
# Unit tests
./mvnw test

# Specific test classes
./mvnw test -Dtest=JwtServiceTest
./mvnw test -Dtest=AuthenticationServiceTest

# Integration tests with Testcontainers
./mvnw test -Dtest=DatabaseIntegrationTest

# All tests
./mvnw verify
```

### Test Coverage
The project includes comprehensive test coverage for:
- **JWT Service**: Token generation, validation, claim extraction, expiration handling, and edge cases
- **Authentication Service**: Login, logout, token refresh, and session management
- **User Service**: User CRUD operations, password management, and validation
- **Repository Layer**: Database operations with PostgreSQL-specific features
- **Integration Tests**: End-to-end testing with real database containers

### Test Database
- **Unit Tests**: H2 in-memory database
- **Integration Tests**: PostgreSQL via Testcontainers
- **Development**: Docker Compose PostgreSQL

## 🐳 Docker Support

### Development Environment
```bash
# Start PostgreSQL for development
docker-compose up -d

# Stop services
docker-compose down
```

### Docker Compose Configuration
- **PostgreSQL 17**: Main database service
- **Health Checks**: Automatic service health monitoring
- **Volume Persistence**: Data persistence across restarts
- **Port Mapping**: 54320:5432 to avoid conflicts

## 📊 Monitoring & Logging

### Logging Configuration
- **Application Logs**: Configurable levels per package
- **Security Events**: Authentication attempts and failures
- **Database Operations**: Flyway migration logs
- **JWT Operations**: Token generation and validation logs

### Health Checks
```http
GET /actuator/health
```

## 🔄 Session Management

### Session Types
- **MOBILE**: Mobile app sessions with push token support
- **WEB**: Web browser sessions with standard token expiration

### Session Features
- **Active Session Tracking**: Monitor user sessions across devices
- **Session Invalidation**: Force logout from all or specific sessions
- **Push Token Integration**: Automatic cleanup on mobile logout
- **Concurrent Sessions**: Support for multiple active sessions per user

## 🚀 Deployment

### Build Application
```bash
# Clean build
./mvnw clean package

# Skip tests
./mvnw clean package -DskipTests

# Create Docker image
./mvnw spring-boot:build-image
```

### Environment Setup
1. **Database**: Ensure PostgreSQL 17+ is available
2. **Environment Variables**: Configure JWT secrets and database credentials
3. **Flyway**: Migrations will run automatically on startup
4. **Health Checks**: Monitor `/actuator/health` endpoint

## 🤝 Contributing

### Development Workflow
1. **Feature Branches**: Create feature branches from main
2. **Testing**: Ensure all tests pass before PR
3. **Code Style**: Follow Spring Boot conventions
4. **Documentation**: Update API documentation for changes

### Code Standards
- **Java 21**: Use modern Java features appropriately
- **Spring Boot**: Follow Spring Boot best practices
- **Security**: Security-first approach for all endpoints
- **Testing**: Comprehensive unit and integration tests

## 📝 License

This project is part of the GEDSYS platform and is proprietary software.

## 🆘 Support

For support and questions:
- **Documentation**: Check this README and inline code documentation
- **Issues**: Create GitHub issues for bugs and feature requests
- **Development**: Follow the contributing guidelines for development setup

---

**Version**: 0.0.1-SNAPSHOT  
**Spring Boot**: 3.5.4  
**Java**: 21  
**Database**: PostgreSQL 17
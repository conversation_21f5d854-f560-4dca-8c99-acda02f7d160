package co.com.gedsys.authentication.exception;

/**
 * Exception thrown when a JWT token or refresh token has expired.
 */
public class TokenExpiredException extends RuntimeException {
    
    public TokenExpiredException(String message) {
        super(message);
    }
    
    public TokenExpiredException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public TokenExpiredException() {
        super("Token has expired");
    }
    
    public TokenExpiredException(String tokenType, String message) {
        super(tokenType + " token has expired: " + message);
    }
}
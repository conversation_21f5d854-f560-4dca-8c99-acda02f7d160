package co.com.gedsys.authentication.exception;

/**
 * Exception thrown when a JWT token or refresh token is invalid or malformed.
 */
public class InvalidTokenException extends RuntimeException {
    
    public InvalidTokenException(String message) {
        super(message);
    }
    
    public InvalidTokenException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public InvalidTokenException() {
        super("Invalid token");
    }
    
    public InvalidTokenException(String tokenType, String reason) {
        super("Invalid " + tokenType + " token: " + reason);
    }
}
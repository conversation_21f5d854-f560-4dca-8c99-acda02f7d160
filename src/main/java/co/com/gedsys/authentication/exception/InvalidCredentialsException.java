package co.com.gedsys.authentication.exception;

/**
 * Exception thrown when user provides invalid credentials during authentication.
 */
public class InvalidCredentialsException extends RuntimeException {
    
    public InvalidCredentialsException(String message) {
        super(message);
    }
    
    public InvalidCredentialsException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public InvalidCredentialsException() {
        super("Invalid email or password");
    }
}
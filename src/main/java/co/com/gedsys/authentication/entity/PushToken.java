package co.com.gedsys.authentication.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * PushToken entity for managing push notification tokens with one-to-one user relationship.
 * Ensures only one push token per user for proper notification delivery.
 */
@Entity
@Table(name = "push_tokens",
       uniqueConstraints = {
           @UniqueConstraint(name = "uk_push_tokens_user", columnNames = {"user_id"})
       },
       indexes = {
           @Index(name = "idx_push_tokens_user_id", columnList = "user_id"),
           @Index(name = "idx_push_tokens_device_type", columnList = "device_type")
       })
public class PushToken {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;
    
    @Column(name = "token", nullable = false, length = 500)
    @NotBlank(message = "Push token is required")
    @Size(max = 500, message = "Push token cannot exceed 500 characters")
    private String token;
    
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false, unique = true)
    @NotNull(message = "User is required")
    private User user;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "device_type", nullable = false, length = 20)
    @NotNull(message = "Device type is required")
    private DeviceType deviceType;
    
    @Column(name = "device_id", length = 255)
    @Size(max = 255, message = "Device ID cannot exceed 255 characters")
    private String deviceId;
    
    @Column(name = "created_at", nullable = false, updatable = false)
    @CreationTimestamp
    private LocalDateTime createdAt;
    
    @Column(name = "last_used")
    private LocalDateTime lastUsed;
    
    // Default constructor
    public PushToken() {}
    
    // Constructor with required fields
    public PushToken(String token, User user, DeviceType deviceType) {
        this.token = token;
        this.user = user;
        this.deviceType = deviceType;
    }
    
    // Constructor with all fields
    public PushToken(String token, User user, DeviceType deviceType, String deviceId) {
        this.token = token;
        this.user = user;
        this.deviceType = deviceType;
        this.deviceId = deviceId;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getToken() {
        return token;
    }
    
    public void setToken(String token) {
        this.token = token;
    }
    
    public User getUser() {
        return user;
    }
    
    public void setUser(User user) {
        this.user = user;
    }
    
    public DeviceType getDeviceType() {
        return deviceType;
    }
    
    public void setDeviceType(DeviceType deviceType) {
        this.deviceType = deviceType;
    }
    
    public String getDeviceId() {
        return deviceId;
    }
    
    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getLastUsed() {
        return lastUsed;
    }
    
    public void setLastUsed(LocalDateTime lastUsed) {
        this.lastUsed = lastUsed;
    }
    
    // Utility methods
    public void updateLastUsed() {
        this.lastUsed = LocalDateTime.now();
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PushToken pushToken = (PushToken) o;
        return Objects.equals(id, pushToken.id) && Objects.equals(token, pushToken.token);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id, token);
    }
    
    @Override
    public String toString() {
        return "PushToken{" +
                "id=" + id +
                ", token='" + token.substring(0, Math.min(token.length(), 20)) + "...'" +
                ", userId=" + (user != null ? user.getId() : null) +
                ", deviceType=" + deviceType +
                ", deviceId='" + deviceId + '\'' +
                ", createdAt=" + createdAt +
                ", lastUsed=" + lastUsed +
                '}';
    }
}
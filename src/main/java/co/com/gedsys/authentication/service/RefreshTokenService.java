package co.com.gedsys.authentication.service;

import co.com.gedsys.authentication.config.JwtProperties;
import co.com.gedsys.authentication.entity.RefreshToken;
import co.com.gedsys.authentication.entity.SessionType;
import co.com.gedsys.authentication.entity.User;
import co.com.gedsys.authentication.repository.RefreshTokenRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.List;
import java.util.Optional;

/**
 * Service for managing refresh tokens with single-use policy and session management.
 * Handles token generation, validation, single-use enforcement, and automatic cleanup.
 */
@Service
@Transactional
public class RefreshTokenService {
    
    private static final Logger logger = LoggerFactory.getLogger(RefreshTokenService.class);
    private static final int TOKEN_LENGTH = 32;
    
    private final RefreshTokenRepository refreshTokenRepository;
    private final JwtProperties jwtProperties;
    private final SecureRandom secureRandom;
    
    public RefreshTokenService(RefreshTokenRepository refreshTokenRepository, JwtProperties jwtProperties) {
        this.refreshTokenRepository = refreshTokenRepository;
        this.jwtProperties = jwtProperties;
        this.secureRandom = new SecureRandom();
    }
    
    /**
     * Generate a new refresh token for a user and session type.
     * Invalidates any existing token for the same user and session type.
     * 
     * @param user the user to generate token for
     * @param sessionType the session type (MOBILE or WEB)
     * @return the generated refresh token
     */
    public RefreshToken generateRefreshToken(User user, SessionType sessionType) {
        logger.debug("Generating refresh token for user {} with session type {}", user.getId(), sessionType);
        
        // Invalidate existing token for this user and session type
        invalidateExistingToken(user, sessionType);
        
        // Generate new token
        String tokenValue = generateSecureToken();
        LocalDateTime expiryDate = LocalDateTime.now().plusSeconds(jwtProperties.getRefreshTokenExpiration() / 1000);
        
        RefreshToken refreshToken = new RefreshToken(tokenValue, user, sessionType, expiryDate);
        RefreshToken savedToken = refreshTokenRepository.save(refreshToken);
        
        logger.info("Refresh token generated successfully for user {} with session type {}", user.getId(), sessionType);
        return savedToken;
    }
    
    /**
     * Validate and consume a refresh token (single-use policy).
     * Marks the token as used after validation.
     * 
     * @param tokenValue the token string to validate
     * @return the refresh token if valid
     * @throws IllegalArgumentException if token is invalid, expired, or already used
     */
    public RefreshToken validateAndConsumeToken(String tokenValue) {
        logger.debug("Validating and consuming refresh token");
        
        LocalDateTime currentTime = LocalDateTime.now();
        Optional<RefreshToken> tokenOpt = refreshTokenRepository.findValidTokenByToken(tokenValue, currentTime);
        
        if (tokenOpt.isEmpty()) {
            logger.warn("Invalid or expired refresh token provided");
            throw new IllegalArgumentException("Invalid or expired refresh token");
        }
        
        RefreshToken refreshToken = tokenOpt.get();
        
        // Mark token as used (single-use policy)
        refreshToken.markAsUsed();
        refreshTokenRepository.save(refreshToken);
        
        logger.info("Refresh token validated and consumed for user {} with session type {}", 
                   refreshToken.getUser().getId(), refreshToken.getSessionType());
        
        return refreshToken;
    }
    
    /**
     * Find a refresh token by its value without consuming it.
     * 
     * @param tokenValue the token string to find
     * @return Optional containing the refresh token if found
     */
    @Transactional(readOnly = true)
    public Optional<RefreshToken> findByToken(String tokenValue) {
        return refreshTokenRepository.findByToken(tokenValue);
    }
    
    /**
     * Check if a token is valid (not used and not expired).
     * 
     * @param tokenValue the token string to check
     * @return true if token is valid, false otherwise
     */
    @Transactional(readOnly = true)
    public boolean isTokenValid(String tokenValue) {
        LocalDateTime currentTime = LocalDateTime.now();
        return refreshTokenRepository.findValidTokenByToken(tokenValue, currentTime).isPresent();
    }
    
    /**
     * Get all valid tokens for a user.
     * Only administrators can access user token information.
     * 
     * @param user the user to get tokens for
     * @return list of valid refresh tokens
     */
    @PreAuthorize("hasRole('ADMIN')")
    @Transactional(readOnly = true)
    public List<RefreshToken> getValidTokensForUser(User user) {
        LocalDateTime currentTime = LocalDateTime.now();
        return refreshTokenRepository.findValidTokensByUser(user, currentTime);
    }
    
    /**
     * Check if user has a valid token for a specific session type.
     * 
     * @param user the user to check
     * @param sessionType the session type to check
     * @return true if user has valid token for session type
     */
    @Transactional(readOnly = true)
    public boolean hasValidTokenForSessionType(User user, SessionType sessionType) {
        LocalDateTime currentTime = LocalDateTime.now();
        return refreshTokenRepository.hasValidTokenForSessionType(user, sessionType, currentTime);
    }
    
    /**
     * Invalidate all tokens for a user.
     * Used during logout or when user account is disabled.
     * 
     * @param user the user whose tokens should be invalidated
     */
    public void invalidateAllUserTokens(User user) {
        logger.debug("Invalidating all tokens for user {}", user.getId());
        
        List<RefreshToken> userTokens = refreshTokenRepository.findByUser(user);
        userTokens.forEach(token -> {
            token.markAsUsed();
            refreshTokenRepository.save(token);
        });
        
        logger.info("All tokens invalidated for user {}", user.getId());
    }
    
    /**
     * Invalidate tokens for a specific user and session type.
     * Used when user logs in on a new device of the same type.
     * 
     * @param user the user whose tokens should be invalidated
     * @param sessionType the session type to invalidate
     */
    public void invalidateTokensForSessionType(User user, SessionType sessionType) {
        logger.debug("Invalidating tokens for user {} with session type {}", user.getId(), sessionType);
        
        refreshTokenRepository.markTokensAsUsedByUserAndSessionType(user, sessionType);
        
        logger.info("Tokens invalidated for user {} with session type {}", user.getId(), sessionType);
    }
    
    /**
     * Delete a specific refresh token.
     * 
     * @param refreshToken the token to delete
     */
    public void deleteToken(RefreshToken refreshToken) {
        logger.debug("Deleting refresh token for user {} with session type {}", 
                    refreshToken.getUser().getId(), refreshToken.getSessionType());
        
        refreshTokenRepository.delete(refreshToken);
        
        logger.info("Refresh token deleted for user {} with session type {}", 
                   refreshToken.getUser().getId(), refreshToken.getSessionType());
    }
    
    /**
     * Delete all tokens for a user.
     * Used during user deletion.
     * 
     * @param user the user whose tokens should be deleted
     */
    public void deleteAllUserTokens(User user) {
        logger.debug("Deleting all tokens for user {}", user.getId());
        
        refreshTokenRepository.deleteByUser(user);
        
        logger.info("All tokens deleted for user {}", user.getId());
    }
    
    /**
     * Delete tokens for a specific user and session type.
     * 
     * @param user the user whose tokens should be deleted
     * @param sessionType the session type to delete
     */
    public void deleteTokensForSessionType(User user, SessionType sessionType) {
        logger.debug("Deleting tokens for user {} with session type {}", user.getId(), sessionType);
        
        refreshTokenRepository.deleteByUserAndSessionType(user, sessionType);
        
        logger.info("Tokens deleted for user {} with session type {}", user.getId(), sessionType);
    }
    
    /**
     * Cleanup expired and used tokens.
     * This method is scheduled to run periodically.
     */
    @Scheduled(fixedRate = 3600000) // Run every hour
    public void cleanupExpiredAndUsedTokens() {
        logger.debug("Starting cleanup of expired and used tokens");
        
        LocalDateTime currentTime = LocalDateTime.now();
        
        // Delete expired tokens
        int expiredCount = refreshTokenRepository.deleteExpiredTokens(currentTime);
        logger.info("Deleted {} expired refresh tokens", expiredCount);
        
        // Delete used tokens
        int usedCount = refreshTokenRepository.deleteUsedTokens();
        logger.info("Deleted {} used refresh tokens", usedCount);
        
        logger.info("Token cleanup completed. Deleted {} expired and {} used tokens", expiredCount, usedCount);
    }
    
    /**
     * Get count of valid tokens for a user.
     * 
     * @param user the user to count tokens for
     * @return count of valid tokens
     */
    @Transactional(readOnly = true)
    public long getValidTokenCountForUser(User user) {
        LocalDateTime currentTime = LocalDateTime.now();
        return refreshTokenRepository.countValidTokensByUser(user, currentTime);
    }
    
    /**
     * Generate a secure random token string.
     * 
     * @return base64 encoded secure random token
     */
    private String generateSecureToken() {
        byte[] tokenBytes = new byte[TOKEN_LENGTH];
        secureRandom.nextBytes(tokenBytes);
        return Base64.getUrlEncoder().withoutPadding().encodeToString(tokenBytes);
    }
    
    /**
     * Invalidate existing token for user and session type.
     * This enforces the single session policy per session type.
     * 
     * @param user the user
     * @param sessionType the session type
     */
    private void invalidateExistingToken(User user, SessionType sessionType) {
        Optional<RefreshToken> existingToken = refreshTokenRepository.findByUserAndSessionType(user, sessionType);
        
        if (existingToken.isPresent()) {
            logger.debug("Invalidating existing token for user {} with session type {}", user.getId(), sessionType);
            RefreshToken token = existingToken.get();
            token.markAsUsed();
            refreshTokenRepository.save(token);
        }
    }
}
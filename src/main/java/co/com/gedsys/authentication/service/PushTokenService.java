package co.com.gedsys.authentication.service;

import co.com.gedsys.authentication.entity.DeviceType;
import co.com.gedsys.authentication.entity.PushToken;
import co.com.gedsys.authentication.entity.User;
import co.com.gedsys.authentication.repository.PushTokenRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Service for managing push notification tokens with single token per user policy.
 * Handles registration, updates, cleanup, and ensures proper token management for mobile sessions.
 */
@Service
@Transactional
public class PushTokenService {
    
    private static final Logger logger = LoggerFactory.getLogger(PushTokenService.class);
    private static final int STALE_TOKEN_DAYS = 30; // Consider tokens stale after 30 days of inactivity
    
    private final PushTokenRepository pushTokenRepository;
    
    public PushTokenService(PushTokenRepository pushTokenRepository) {
        this.pushTokenRepository = pushTokenRepository;
    }
    
    /**
     * Register or update a push token for a user.
     * Replaces any existing push token for the user (single token per user policy).
     * 
     * @param user the user to register the token for
     * @param tokenValue the push token value
     * @param deviceType the device type (ANDROID, IOS)
     * @param deviceId optional device identifier
     * @return the registered push token
     */
    public PushToken registerPushToken(User user, String tokenValue, DeviceType deviceType, String deviceId) {
        logger.debug("Registering push token for user {} with device type {}", user.getId(), deviceType);
        
        // Remove existing push token for this user (single token per user policy)
        Optional<PushToken> existingToken = pushTokenRepository.findByUser(user);
        if (existingToken.isPresent()) {
            logger.debug("Removing existing push token for user {}", user.getId());
            pushTokenRepository.delete(existingToken.get());
        }
        
        // Create new push token
        PushToken pushToken = new PushToken(tokenValue, user, deviceType, deviceId);
        pushToken.updateLastUsed(); // Set initial last used timestamp
        
        PushToken savedToken = pushTokenRepository.save(pushToken);
        
        logger.info("Push token registered successfully for user {} with device type {}", user.getId(), deviceType);
        return savedToken;
    }
    
    /**
     * Update an existing push token for a user.
     * 
     * @param user the user whose token should be updated
     * @param tokenValue the new push token value
     * @param deviceType the device type
     * @param deviceId optional device identifier
     * @return the updated push token
     * @throws IllegalArgumentException if user doesn't have an existing push token
     */
    public PushToken updatePushToken(User user, String tokenValue, DeviceType deviceType, String deviceId) {
        logger.debug("Updating push token for user {} with device type {}", user.getId(), deviceType);
        
        Optional<PushToken> existingTokenOpt = pushTokenRepository.findByUser(user);
        if (existingTokenOpt.isEmpty()) {
            logger.warn("Attempt to update non-existent push token for user {}", user.getId());
            throw new IllegalArgumentException("User does not have an existing push token");
        }
        
        PushToken existingToken = existingTokenOpt.get();
        existingToken.setToken(tokenValue);
        existingToken.setDeviceType(deviceType);
        existingToken.setDeviceId(deviceId);
        existingToken.updateLastUsed();
        
        PushToken savedToken = pushTokenRepository.save(existingToken);
        
        logger.info("Push token updated successfully for user {} with device type {}", user.getId(), deviceType);
        return savedToken;
    }
    
    /**
     * Get push token for a user.
     * 
     * @param user the user to get the token for
     * @return Optional containing the push token if found
     */
    @Transactional(readOnly = true)
    public Optional<PushToken> getPushTokenByUser(User user) {
        logger.debug("Getting push token for user {}", user.getId());
        return pushTokenRepository.findByUser(user);
    }
    
    /**
     * Get push token by token value.
     * 
     * @param tokenValue the token value to search for
     * @return Optional containing the push token if found
     */
    @Transactional(readOnly = true)
    public Optional<PushToken> getPushTokenByValue(String tokenValue) {
        return pushTokenRepository.findByToken(tokenValue);
    }
    
    /**
     * Check if a user has a push token.
     * 
     * @param user the user to check
     * @return true if user has a push token, false otherwise
     */
    @Transactional(readOnly = true)
    public boolean hasPushToken(User user) {
        return pushTokenRepository.existsByUser(user);
    }
    
    /**
     * Remove push token for a user.
     * Used during mobile logout or when invalidating push notifications.
     * 
     * @param user the user whose push token should be removed
     * @return true if a token was removed, false if no token existed
     */
    public boolean removePushToken(User user) {
        logger.debug("Removing push token for user {}", user.getId());
        
        if (!pushTokenRepository.existsByUser(user)) {
            logger.debug("No push token found for user {}", user.getId());
            return false;
        }
        
        pushTokenRepository.deleteByUser(user);
        logger.info("Push token removed successfully for user {}", user.getId());
        return true;
    }
    
    /**
     * Remove push token by token value.
     * 
     * @param tokenValue the token value to remove
     * @return true if a token was removed, false if no token existed
     */
    public boolean removePushTokenByValue(String tokenValue) {
        logger.debug("Removing push token by value");
        
        if (!pushTokenRepository.existsByToken(tokenValue)) {
            logger.debug("No push token found with the provided value");
            return false;
        }
        
        pushTokenRepository.deleteByToken(tokenValue);
        logger.info("Push token removed successfully by value");
        return true;
    }
    
    /**
     * Update the last used timestamp for a user's push token.
     * This should be called when sending notifications to track token usage.
     * 
     * @param user the user whose token should be updated
     */
    public void updateLastUsed(User user) {
        logger.debug("Updating last used timestamp for user {}", user.getId());
        
        LocalDateTime now = LocalDateTime.now();
        int updatedCount = pushTokenRepository.updateLastUsedByUser(user, now);
        
        if (updatedCount > 0) {
            logger.debug("Last used timestamp updated for user {}", user.getId());
        } else {
            logger.debug("No push token found to update for user {}", user.getId());
        }
    }
    
    /**
     * Update the last used timestamp for a push token by token value.
     * 
     * @param tokenValue the token value to update
     */
    public void updateLastUsedByToken(String tokenValue) {
        logger.debug("Updating last used timestamp for token");
        
        LocalDateTime now = LocalDateTime.now();
        int updatedCount = pushTokenRepository.updateLastUsedByToken(tokenValue, now);
        
        if (updatedCount > 0) {
            logger.debug("Last used timestamp updated for token");
        } else {
            logger.debug("No push token found to update with the provided value");
        }
    }
    
    /**
     * Get all push tokens by device type.
     * 
     * @param deviceType the device type to filter by
     * @return list of push tokens with the specified device type
     */
    @Transactional(readOnly = true)
    public List<PushToken> getPushTokensByDeviceType(DeviceType deviceType) {
        logger.debug("Getting push tokens by device type {}", deviceType);
        return pushTokenRepository.findByDeviceType(deviceType);
    }
    
    /**
     * Get push tokens by device ID.
     * 
     * @param deviceId the device ID to search for
     * @return list of push tokens with the specified device ID
     */
    @Transactional(readOnly = true)
    public List<PushToken> getPushTokensByDeviceId(String deviceId) {
        logger.debug("Getting push tokens by device ID {}", deviceId);
        return pushTokenRepository.findByDeviceId(deviceId);
    }
    
    /**
     * Get count of push tokens by device type.
     * 
     * @param deviceType the device type to count
     * @return count of push tokens with the specified device type
     */
    @Transactional(readOnly = true)
    public long getCountByDeviceType(DeviceType deviceType) {
        return pushTokenRepository.countByDeviceType(deviceType);
    }
    
    /**
     * Get all push tokens created after a specific date.
     * 
     * @param date the date to compare against
     * @return list of push tokens created after the specified date
     */
    @Transactional(readOnly = true)
    public List<PushToken> getTokensCreatedAfter(LocalDateTime date) {
        return pushTokenRepository.findByCreatedAtAfter(date);
    }
    
    /**
     * Get stale push tokens that haven't been used recently.
     * 
     * @return list of stale push tokens
     */
    @Transactional(readOnly = true)
    public List<PushToken> getStaleTokens() {
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(STALE_TOKEN_DAYS);
        return pushTokenRepository.findStaleTokens(cutoffDate);
    }
    
    /**
     * Cleanup stale push tokens that haven't been used recently.
     * This method is scheduled to run periodically to maintain database hygiene.
     */
    @Scheduled(fixedRate = 86400000) // Run daily (24 hours)
    public void cleanupStaleTokens() {
        logger.debug("Starting cleanup of stale push tokens");
        
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(STALE_TOKEN_DAYS);
        int deletedCount = pushTokenRepository.deleteStaleTokens(cutoffDate);
        
        logger.info("Cleanup completed. Deleted {} stale push tokens older than {} days", deletedCount, STALE_TOKEN_DAYS);
    }
    
    /**
     * Validate that a push token should only be registered for mobile sessions.
     * This method can be used by authentication services to enforce business rules.
     * 
     * @param deviceType the device type to validate
     * @throws IllegalArgumentException if device type is not mobile
     */
    public void validateMobileDeviceType(DeviceType deviceType) {
        if (deviceType == DeviceType.WEB) {
            throw new IllegalArgumentException("Push tokens are only supported for mobile devices (ANDROID, IOS)");
        }
    }
    
    /**
     * Get total count of all push tokens.
     * Only administrators can access token statistics.
     * 
     * @return total count of push tokens
     */
    @PreAuthorize("hasRole('ADMIN')")
    @Transactional(readOnly = true)
    public long getTotalTokenCount() {
        return pushTokenRepository.count();
    }
    
    /**
     * Check if a token value already exists in the system.
     * 
     * @param tokenValue the token value to check
     * @return true if token exists, false otherwise
     */
    @Transactional(readOnly = true)
    public boolean tokenExists(String tokenValue) {
        return pushTokenRepository.existsByToken(tokenValue);
    }
}
package co.com.gedsys.authentication.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "app.jwt")
public class JwtProperties {
    
    private String secret;
    private long accessTokenExpiration;
    private long refreshTokenExpiration;
    
    private Mobile mobile = new Mobile();
    private Web web = new Web();
    
    // Getters and Setters
    public String getSecret() {
        return secret;
    }
    
    public void setSecret(String secret) {
        this.secret = secret;
    }
    
    public long getAccessTokenExpiration() {
        return accessTokenExpiration;
    }
    
    public void setAccessTokenExpiration(long accessTokenExpiration) {
        this.accessTokenExpiration = accessTokenExpiration;
    }
    
    public long getRefreshTokenExpiration() {
        return refreshTokenExpiration;
    }
    
    public void setRefreshTokenExpiration(long refreshTokenExpiration) {
        this.refreshTokenExpiration = refreshTokenExpiration;
    }
    
    public Mobile getMobile() {
        return mobile;
    }
    
    public void setMobile(Mobile mobile) {
        this.mobile = mobile;
    }
    
    public Web getWeb() {
        return web;
    }
    
    public void setWeb(Web web) {
        this.web = web;
    }
    
    public static class Mobile {
        private long accessTokenExpiration = 7200000; // 2 hours default
        
        public long getAccessTokenExpiration() {
            return accessTokenExpiration;
        }
        
        public void setAccessTokenExpiration(long accessTokenExpiration) {
            this.accessTokenExpiration = accessTokenExpiration;
        }
    }
    
    public static class Web {
        private long accessTokenExpiration = 3600000; // 1 hour default
        
        public long getAccessTokenExpiration() {
            return accessTokenExpiration;
        }
        
        public void setAccessTokenExpiration(long accessTokenExpiration) {
            this.accessTokenExpiration = accessTokenExpiration;
        }
    }
    
    /**
     * Get access token expiration based on session type
     */
    public long getAccessTokenExpirationForSessionType(String sessionType) {
        if ("MOBILE".equalsIgnoreCase(sessionType)) {
            return mobile.getAccessTokenExpiration();
        } else if ("WEB".equalsIgnoreCase(sessionType)) {
            return web.getAccessTokenExpiration();
        }
        return accessTokenExpiration; // default fallback
    }
}
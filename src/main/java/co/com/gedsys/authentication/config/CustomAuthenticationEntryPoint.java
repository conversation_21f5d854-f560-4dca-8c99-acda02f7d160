package co.com.gedsys.authentication.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Custom authentication entry point for handling 401 Unauthorized errors.
 * Provides consistent JSON error responses for authentication failures.
 */
@Component
public class CustomAuthenticationEntryPoint implements AuthenticationEntryPoint {
    
    private static final Logger logger = LoggerFactory.getLogger(CustomAuthenticationEntryPoint.class);
    
    private final ObjectMapper objectMapper;
    
    public CustomAuthenticationEntryPoint(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }
    
    /**
     * Handles authentication failures by returning a standardized JSON error response.
     * 
     * @param request the HTTP request
     * @param response the HTTP response
     * @param authException the authentication exception that triggered this entry point
     * @throws IOException if an I/O error occurs
     * @throws ServletException if a servlet error occurs
     */
    @Override
    public void commence(HttpServletRequest request, 
                        HttpServletResponse response,
                        AuthenticationException authException) throws IOException, ServletException {
        
        // Log the authentication failure
        String requestUri = request.getRequestURI();
        String method = request.getMethod();
        String remoteAddr = getClientIpAddress(request);
        
        logger.warn("Authentication failed for {} {} from IP {}: {}", 
                   method, requestUri, remoteAddr, authException.getMessage());
        
        // Determine the specific error message based on the exception type
        String errorMessage = determineErrorMessage(authException, request);
        String errorCode = determineErrorCode(authException);
        
        // Create error response
        Map<String, Object> errorResponse = createErrorResponse(
            errorCode,
            errorMessage,
            HttpStatus.UNAUTHORIZED.value(),
            requestUri
        );
        
        // Set response properties
        response.setStatus(HttpStatus.UNAUTHORIZED.value());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding("UTF-8");
        
        // Add security headers
        addSecurityHeaders(response);
        
        // Write JSON response
        response.getWriter().write(objectMapper.writeValueAsString(errorResponse));
        response.getWriter().flush();
    }
    
    /**
     * Determine the appropriate error message based on the authentication exception.
     * 
     * @param authException the authentication exception
     * @param request the HTTP request
     * @return the error message
     */
    private String determineErrorMessage(AuthenticationException authException, HttpServletRequest request) {
        String authHeader = request.getHeader("Authorization");
        
        // Check if no authorization header is present
        if (authHeader == null || authHeader.trim().isEmpty()) {
            return "Authentication required. Please provide a valid JWT token.";
        }
        
        // Check if authorization header format is invalid
        if (!authHeader.startsWith("Bearer ")) {
            return "Invalid authorization header format. Expected 'Bearer <token>'.";
        }
        
        // Check if token is present but invalid/expired
        String token = authHeader.substring(7).trim();
        if (token.isEmpty()) {
            return "Missing JWT token in authorization header.";
        }
        
        // For other authentication failures, provide a generic message
        return "Invalid or expired JWT token. Please authenticate again.";
    }
    
    /**
     * Determine the error code based on the authentication exception type.
     * 
     * @param authException the authentication exception
     * @return the error code
     */
    private String determineErrorCode(AuthenticationException authException) {
        String exceptionName = authException.getClass().getSimpleName();
        
        return switch (exceptionName) {
            case "BadCredentialsException" -> "INVALID_CREDENTIALS";
            case "InsufficientAuthenticationException" -> "INSUFFICIENT_AUTHENTICATION";
            case "AccountExpiredException" -> "ACCOUNT_EXPIRED";
            case "CredentialsExpiredException" -> "CREDENTIALS_EXPIRED";
            case "DisabledException" -> "ACCOUNT_DISABLED";
            case "LockedException" -> "ACCOUNT_LOCKED";
            default -> "AUTHENTICATION_FAILED";
        };
    }
    
    /**
     * Create a standardized error response map.
     * 
     * @param error the error code
     * @param message the error message
     * @param status the HTTP status code
     * @param path the request path
     * @return the error response map
     */
    private Map<String, Object> createErrorResponse(String error, String message, int status, String path) {
        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("error", error);
        errorResponse.put("message", message);
        errorResponse.put("status", status);
        errorResponse.put("timestamp", LocalDateTime.now().toString());
        errorResponse.put("path", path);
        
        return errorResponse;
    }
    
    /**
     * Add security headers to the response.
     * 
     * @param response the HTTP response
     */
    private void addSecurityHeaders(HttpServletResponse response) {
        // Prevent caching of error responses
        response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Expires", "0");
        
        // Security headers
        response.setHeader("X-Content-Type-Options", "nosniff");
        response.setHeader("X-Frame-Options", "DENY");
        response.setHeader("X-XSS-Protection", "1; mode=block");
    }
    
    /**
     * Extract the client IP address from the request, considering proxy headers.
     * 
     * @param request the HTTP request
     * @return the client IP address
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            // X-Forwarded-For can contain multiple IPs, take the first one
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
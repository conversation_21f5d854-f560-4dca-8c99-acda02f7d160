package co.com.gedsys.authentication.dto;

import co.com.gedsys.authentication.entity.SessionType;

import java.time.LocalDateTime;

/**
 * DTO for active session information in admin responses.
 */
public class ActiveSessionInfo {
    
    private SessionType sessionType;
    private LocalDateTime lastActivity;
    private LocalDateTime expiresAt;
    
    // Default constructor
    public ActiveSessionInfo() {}
    
    // Constructor with all fields
    public ActiveSessionInfo(SessionType sessionType, LocalDateTime lastActivity, LocalDateTime expiresAt) {
        this.sessionType = sessionType;
        this.lastActivity = lastActivity;
        this.expiresAt = expiresAt;
    }
    
    // Getters and Setters
    public SessionType getSessionType() {
        return sessionType;
    }
    
    public void setSessionType(SessionType sessionType) {
        this.sessionType = sessionType;
    }
    
    public LocalDateTime getLastActivity() {
        return lastActivity;
    }
    
    public void setLastActivity(LocalDateTime lastActivity) {
        this.lastActivity = lastActivity;
    }
    
    public LocalDateTime getExpiresAt() {
        return expiresAt;
    }
    
    public void setExpiresAt(LocalDateTime expiresAt) {
        this.expiresAt = expiresAt;
    }
    
    // Utility method to check if session is expired
    public boolean isExpired() {
        return expiresAt != null && LocalDateTime.now().isAfter(expiresAt);
    }
}
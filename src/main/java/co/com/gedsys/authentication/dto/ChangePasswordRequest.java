package co.com.gedsys.authentication.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * DTO for password change requests with validation.
 */
public class ChangePasswordRequest {
    
    @NotBlank(message = "Current password is required")
    @Size(min = 6, message = "Current password must be at least 6 characters")
    private String currentPassword;
    
    @NotBlank(message = "New password is required")
    @Size(min = 6, message = "New password must be at least 6 characters")
    private String newPassword;
    
    @NotBlank(message = "Password confirmation is required")
    @Size(min = 6, message = "Password confirmation must be at least 6 characters")
    private String confirmNewPassword;
    
    // Default constructor
    public ChangePasswordRequest() {}
    
    // Constructor with all fields
    public ChangePasswordRequest(String currentPassword, String newPassword, String confirmNewPassword) {
        this.currentPassword = currentPassword;
        this.newPassword = newPassword;
        this.confirmNewPassword = confirmNewPassword;
    }
    
    // Getters and Setters
    public String getCurrentPassword() {
        return currentPassword;
    }
    
    public void setCurrentPassword(String currentPassword) {
        this.currentPassword = currentPassword;
    }
    
    public String getNewPassword() {
        return newPassword;
    }
    
    public void setNewPassword(String newPassword) {
        this.newPassword = newPassword;
    }
    
    public String getConfirmNewPassword() {
        return confirmNewPassword;
    }
    
    public void setConfirmNewPassword(String confirmNewPassword) {
        this.confirmNewPassword = confirmNewPassword;
    }
    
    // Utility method to check if passwords match
    public boolean isPasswordConfirmationValid() {
        return newPassword != null && newPassword.equals(confirmNewPassword);
    }
    
    // Utility method to check if new password is different from current
    public boolean isNewPasswordDifferent() {
        return currentPassword != null && newPassword != null && !currentPassword.equals(newPassword);
    }
}
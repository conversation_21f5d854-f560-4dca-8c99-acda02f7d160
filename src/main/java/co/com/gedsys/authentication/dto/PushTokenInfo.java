package co.com.gedsys.authentication.dto;

import co.com.gedsys.authentication.entity.DeviceType;
import co.com.gedsys.authentication.entity.PushToken;

import java.time.LocalDateTime;

/**
 * DTO for push token information in admin responses.
 */
public class PushTokenInfo {
    
    private String deviceType;
    private String deviceId;
    private LocalDateTime createdAt;
    private LocalDateTime lastUsed;
    
    // Default constructor
    public PushTokenInfo() {}
    
    // Constructor from PushToken entity
    public PushTokenInfo(PushToken pushToken) {
        if (pushToken != null) {
            this.deviceType = pushToken.getDeviceType() != null ? pushToken.getDeviceType().name() : null;
            this.deviceId = pushToken.getDeviceId();
            this.createdAt = pushToken.getCreatedAt();
            this.lastUsed = pushToken.getLastUsed();
        }
    }
    
    // Constructor with all fields
    public PushTokenInfo(String deviceType, String deviceId, LocalDateTime createdAt, LocalDateTime lastUsed) {
        this.deviceType = deviceType;
        this.deviceId = deviceId;
        this.createdAt = createdAt;
        this.lastUsed = lastUsed;
    }
    
    // Getters and Setters
    public String getDeviceType() {
        return deviceType;
    }
    
    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }
    
    public String getDeviceId() {
        return deviceId;
    }
    
    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getLastUsed() {
        return lastUsed;
    }
    
    public void setLastUsed(LocalDateTime lastUsed) {
        this.lastUsed = lastUsed;
    }
}
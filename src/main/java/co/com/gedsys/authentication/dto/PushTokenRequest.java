package co.com.gedsys.authentication.dto;

import co.com.gedsys.authentication.entity.DeviceType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * DTO for push token registration requests.
 */
public class PushTokenRequest {
    
    @NotBlank(message = "Push token is required")
    private String pushToken;
    
    @NotNull(message = "Device type is required")
    private DeviceType deviceType;
    
    private String deviceId;
    
    // Default constructor
    public PushTokenRequest() {}
    
    // Constructor with required fields
    public PushTokenRequest(String pushToken, DeviceType deviceType) {
        this.pushToken = pushToken;
        this.deviceType = deviceType;
    }
    
    // Constructor with all fields
    public PushTokenRequest(String pushToken, DeviceType deviceType, String deviceId) {
        this.pushToken = pushToken;
        this.deviceType = deviceType;
        this.deviceId = deviceId;
    }
    
    // Getters and Setters
    public String getPushToken() {
        return pushToken;
    }
    
    public void setPushToken(String pushToken) {
        this.pushToken = pushToken;
    }
    
    public DeviceType getDeviceType() {
        return deviceType;
    }
    
    public void setDeviceType(DeviceType deviceType) {
        this.deviceType = deviceType;
    }
    
    public String getDeviceId() {
        return deviceId;
    }
    
    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }
    
    // Utility methods
    public boolean hasDeviceId() {
        return deviceId != null && !deviceId.trim().isEmpty();
    }
    
    public boolean isValidForMobileDevice() {
        return pushToken != null && !pushToken.trim().isEmpty() && 
               deviceType != null && (deviceType == DeviceType.ANDROID || deviceType == DeviceType.IOS);
    }
}
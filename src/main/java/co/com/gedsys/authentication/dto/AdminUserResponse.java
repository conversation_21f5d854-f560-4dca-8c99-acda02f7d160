package co.com.gedsys.authentication.dto;

import co.com.gedsys.authentication.entity.Role;
import co.com.gedsys.authentication.entity.User;

import java.time.LocalDateTime;
import java.util.List;

/**
 * DTO for admin user responses with complete user information including sessions and push tokens.
 */
public class AdminUserResponse {
    
    private Long id;
    private String email;
    private String firstName;
    private String lastName;
    private Role role;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private boolean enabled;
    
    // Information about active sessions
    private List<ActiveSessionInfo> activeSessions;
    
    // Push token information if exists
    private PushTokenInfo pushToken;
    
    // Default constructor
    public AdminUserResponse() {}
    
    // Constructor from User entity
    public AdminUserResponse(User user) {
        this.id = user.getId();
        this.email = user.getEmail();
        this.firstName = user.getFirstName();
        this.lastName = user.getLastName();
        this.role = user.getRole();
        this.createdAt = user.getCreatedAt();
        this.updatedAt = user.getUpdatedAt();
        this.enabled = user.isEnabled();
    }
    
    // Constructor with all fields
    public AdminUserResponse(User user, List<ActiveSessionInfo> activeSessions, PushTokenInfo pushToken) {
        this(user);
        this.activeSessions = activeSessions;
        this.pushToken = pushToken;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getFirstName() {
        return firstName;
    }
    
    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }
    
    public String getLastName() {
        return lastName;
    }
    
    public void setLastName(String lastName) {
        this.lastName = lastName;
    }
    
    public Role getRole() {
        return role;
    }
    
    public void setRole(Role role) {
        this.role = role;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
    
    public List<ActiveSessionInfo> getActiveSessions() {
        return activeSessions;
    }
    
    public void setActiveSessions(List<ActiveSessionInfo> activeSessions) {
        this.activeSessions = activeSessions;
    }
    
    public PushTokenInfo getPushToken() {
        return pushToken;
    }
    
    public void setPushToken(PushTokenInfo pushToken) {
        this.pushToken = pushToken;
    }
    
    // Utility methods
    public boolean hasPushToken() {
        return pushToken != null;
    }
    
    public boolean hasActiveSessions() {
        return activeSessions != null && !activeSessions.isEmpty();
    }
    
    public int getActiveSessionCount() {
        return activeSessions != null ? activeSessions.size() : 0;
    }
}
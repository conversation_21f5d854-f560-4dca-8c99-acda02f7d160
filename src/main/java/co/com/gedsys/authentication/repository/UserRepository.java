package co.com.gedsys.authentication.repository;

import co.com.gedsys.authentication.entity.Role;
import co.com.gedsys.authentication.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Repository interface for User entity operations.
 * Provides basic CRUD operations and custom queries for user management.
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    
    /**
     * Find a user by email address.
     * 
     * @param email the email address to search for
     * @return Optional containing the user if found, empty otherwise
     */
    Optional<User> findByEmail(String email);
    
    /**
     * Check if a user exists with the given email address.
     * 
     * @param email the email address to check
     * @return true if a user exists with the email, false otherwise
     */
    boolean existsByEmail(String email);
    
    /**
     * Find users with optional filters and pagination.
     * This query supports filtering by email (partial match), enabled status, and role.
     * 
     * @param email partial email to filter by (case-insensitive), null to ignore
     * @param enabled enabled status to filter by, null to ignore
     * @param role role to filter by, null to ignore
     * @param pageable pagination and sorting parameters
     * @return Page of users matching the criteria
     */
    @Query("SELECT u FROM User u WHERE " +
           "(:email IS NULL OR u.email LIKE %:email%) AND " +
           "(:enabled IS NULL OR u.enabled = :enabled) AND " +
           "(:role IS NULL OR u.role = :role)")
    Page<User> findUsersWithFilters(
            @Param("email") String email,
            @Param("enabled") Boolean enabled,
            @Param("role") Role role,
            Pageable pageable
    );
    
    /**
     * Find all enabled users.
     * 
     * @param pageable pagination and sorting parameters
     * @return Page of enabled users
     */
    Page<User> findByEnabledTrue(Pageable pageable);
    
    /**
     * Find users by role.
     * 
     * @param role the role to filter by
     * @param pageable pagination and sorting parameters
     * @return Page of users with the specified role
     */
    Page<User> findByRole(Role role, Pageable pageable);
    
    /**
     * Count users by enabled status.
     * 
     * @param enabled the enabled status to count
     * @return number of users with the specified enabled status
     */
    long countByEnabled(boolean enabled);
    
    /**
     * Count users by role.
     * 
     * @param role the role to count
     * @return number of users with the specified role
     */
    long countByRole(Role role);
}
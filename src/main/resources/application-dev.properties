# Development Profile Configuration

# Docker Compose Configuration
spring.docker.compose.enabled=true
spring.docker.compose.file=docker-compose.yaml
spring.docker.compose.lifecycle-management=start-and-stop
spring.docker.compose.profiles.active=dev
spring.docker.compose.start.command=up
spring.docker.compose.stop.command=stop
spring.docker.compose.start.log-level=info

# Database Configuration (will be auto-configured by Docker Compose support)
spring.datasource.url=***************************************************
spring.datasource.username=auth_user
spring.datasource.password=auth_password
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA/Hibernate Configuration
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.jdbc.time_zone=UTC

# Connection Pool Configuration (HikariCP)
spring.datasource.hikari.maximum-pool-size=10
spring.datasource.hikari.minimum-idle=2
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.connection-timeout=20000
spring.datasource.hikari.max-lifetime=1200000

# Flyway Configuration
spring.flyway.enabled=true
spring.flyway.locations=classpath:db/migration
spring.flyway.baseline-on-migrate=true
spring.flyway.validate-on-migrate=true

# JWT Settings - Development
app.jwt.secret=${JWT_SECRET:devSecretKey123456789012345678901234567890}
app.jwt.access-token-expiration=${JWT_ACCESS_EXPIRATION:3600000}
app.jwt.refresh-token-expiration=${JWT_REFRESH_EXPIRATION:604800000}

# JWT Configuration by session type
app.jwt.mobile.access-token-expiration=${JWT_MOBILE_ACCESS_EXPIRATION:7200000}
app.jwt.web.access-token-expiration=${JWT_WEB_ACCESS_EXPIRATION:3600000}

# Logging Configuration
logging.level.co.com.gedsys.authentication=DEBUG
logging.level.org.springframework.security=INFO
logging.level.org.flywaydb=INFO
logging.level.org.springframework.docker.compose=DEBUG
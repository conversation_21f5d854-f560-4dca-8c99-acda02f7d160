-- Create push tokens table with one-to-one user relationship
CREATE TABLE push_tokens (
    id BIGSERIAL PRIMARY KEY,
    token VARCHAR(500) NOT NULL,
    user_id BIGINT NOT NULL UNIQUE,
    device_type VARCHAR(20) NOT NULL CHECK (device_type IN ('ANDROID', 'IOS', 'WEB')),
    device_id VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_used TIMESTAMP WITH TIME ZONE,
    
    -- Foreign key constraint to users table with one-to-one relationship
    CONSTRAINT fk_push_tokens_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create indexes for performance optimization
CREATE INDEX idx_push_tokens_user_id ON push_tokens(user_id);
CREATE INDEX idx_push_tokens_device_type ON push_tokens(device_type);
CREATE INDEX idx_push_tokens_device_id ON push_tokens(device_id);
CREATE INDEX idx_push_tokens_created_at ON push_tokens(created_at);
CREATE INDEX idx_push_tokens_last_used ON push_tokens(last_used);

-- Add comments for documentation
COMMENT ON TABLE push_tokens IS 'Push notification tokens with one-to-one user relationship';
COMMENT ON COLUMN push_tokens.token IS 'Push notification token string';
COMMENT ON COLUMN push_tokens.device_type IS 'Device type: ANDROID, IOS, or WEB';
COMMENT ON COLUMN push_tokens.device_id IS 'Unique device identifier';
COMMENT ON COLUMN push_tokens.last_used IS 'Timestamp of last token usage';
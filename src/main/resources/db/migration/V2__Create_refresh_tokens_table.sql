-- Create refresh tokens table with session management support
CREATE TABLE refresh_tokens (
    id BIGSERIAL PRIMARY KEY,
    token VARCHAR(255) NOT NULL UNIQUE,
    user_id BIGINT NOT NULL,
    session_type VARCHAR(20) NOT NULL CHECK (session_type IN ('MOBILE', 'WEB')),
    expiry_date TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    used BOOLEAN NOT NULL DEFAULT false,
    
    -- Foreign key constraint to users table
    CONSTRAINT fk_refresh_tokens_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Unique constraint for user/session type combination (single session policy)
    CONSTRAINT uk_user_session_type UNIQUE (user_id, session_type)
);

-- <PERSON>reate indexes for performance optimization
CREATE INDEX idx_refresh_tokens_user_id ON refresh_tokens(user_id);
CREATE INDEX idx_refresh_tokens_expiry ON refresh_tokens(expiry_date);
CREATE INDEX idx_refresh_tokens_used ON refresh_tokens(used);
CREATE INDEX idx_refresh_tokens_session_type ON refresh_tokens(session_type);
CREATE INDEX idx_refresh_tokens_token ON refresh_tokens(token);

-- Add comments for documentation
COMMENT ON TABLE refresh_tokens IS 'Refresh tokens for session management with single session policy';
COMMENT ON COLUMN refresh_tokens.token IS 'Unique refresh token string';
COMMENT ON COLUMN refresh_tokens.session_type IS 'Session type: MOBILE or WEB';
COMMENT ON COLUMN refresh_tokens.used IS 'Flag to mark single-use tokens as consumed';
COMMENT ON COLUMN refresh_tokens.expiry_date IS 'Token expiration timestamp';
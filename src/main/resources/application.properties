# Database Configuration
spring.datasource.url=**************************************************
spring.datasource.username=${DB_USERNAME:auth_user}
spring.datasource.password=${DB_PASSWORD:auth_password}
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA/Hibernate Configuration
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.jdbc.time_zone=UTC

# Connection Pool Configuration (HikariCP)
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.connection-timeout=20000
spring.datasource.hikari.max-lifetime=1200000

# Flyway Configuration
spring.flyway.enabled=true
spring.flyway.locations=classpath:db/migration
spring.flyway.baseline-on-migrate=true
spring.flyway.validate-on-migrate=true

# JWT Settings - Configurable
app.jwt.secret=${JWT_SECRET:mySecretKey123456789012345678901234567890}
app.jwt.access-token-expiration=${JWT_ACCESS_EXPIRATION:3600000}
app.jwt.refresh-token-expiration=${JWT_REFRESH_EXPIRATION:604800000}

# JWT Configuration by session type
app.jwt.mobile.access-token-expiration=${JWT_MOBILE_ACCESS_EXPIRATION:7200000}
app.jwt.web.access-token-expiration=${JWT_WEB_ACCESS_EXPIRATION:3600000}

# Docker Compose Configuration (disabled by default)
spring.docker.compose.enabled=false

# Logging Configuration
logging.level.co.com.gedsys.authentication=INFO
logging.level.org.springframework.security=DEBUG
logging.level.org.flywaydb=INFO
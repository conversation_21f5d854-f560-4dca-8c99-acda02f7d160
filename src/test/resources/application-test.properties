# Docker Compose Configuration for Tests
spring.docker.compose.enabled=true
spring.docker.compose.file=test.docker-compose.yaml
spring.docker.compose.lifecycle-management=start-and-stop
spring.docker.compose.start.command=up
spring.docker.compose.stop.command=stop
spring.docker.compose.start.log-level=info

# Test Database Configuration (will be auto-configured by Docker Compose support)
spring.datasource.url=*********************************************
spring.datasource.username=test_user
spring.datasource.password=test_password
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA/Hibernate Configuration for Tests
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.jdbc.time_zone=UTC

# Flyway Configuration for Tests
spring.flyway.enabled=true
spring.flyway.locations=classpath:db/migration
spring.flyway.baseline-on-migrate=true
spring.flyway.validate-on-migrate=true
spring.flyway.clean-disabled=false

# JWT Settings for Tests
app.jwt.secret=testSecretKey123456789012345678901234567890
app.jwt.access-token-expiration=3600000
app.jwt.refresh-token-expiration=604800000

# JWT Configuration by session type for Tests
app.jwt.mobile.access-token-expiration=7200000
app.jwt.web.access-token-expiration=3600000

# Logging Configuration for Tests
logging.level.co.com.gedsys.authentication=DEBUG
logging.level.org.springframework.security=INFO
logging.level.org.flywaydb=INFO
logging.level.org.springframework.docker.compose=DEBUG
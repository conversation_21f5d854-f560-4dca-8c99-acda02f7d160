package co.com.gedsys.authentication;

import co.com.gedsys.authentication.entity.*;
import co.com.gedsys.authentication.repository.PushTokenRepository;
import co.com.gedsys.authentication.repository.RefreshTokenRepository;
import co.com.gedsys.authentication.repository.UserRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.test.context.TestPropertySource;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.*;

/**
 * Integration tests for database operations using real PostgreSQL container.
 * Tests Flyway migrations, entity mappings, constraints, and repository operations.
 */
@DataJpaTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@TestPropertySource(properties = {
    "spring.docker.compose.file=compose-test.yaml",
    "spring.datasource.url=*********************************************",
    "spring.datasource.username=test_user",
    "spring.datasource.password=test_password",
    "spring.jpa.hibernate.ddl-auto=validate",
    "spring.flyway.enabled=true",
    "spring.flyway.clean-disabled=false"
})
class DatabaseIntegrationTest {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private RefreshTokenRepository refreshTokenRepository;

    @Autowired
    private PushTokenRepository pushTokenRepository;

    @Autowired
    private TestEntityManager entityManager;

    @Test
    void shouldConnectToDatabase() {
        // Test that repositories are available and can perform basic operations
        assertThat(userRepository).isNotNull();
        assertThat(refreshTokenRepository).isNotNull();
        assertThat(pushTokenRepository).isNotNull();
    }

    @Test
    void shouldApplyFlywayMigrations() {
        // Test that tables exist by performing basic operations
        assertThatNoException().isThrownBy(() -> {
            userRepository.count();
            refreshTokenRepository.count();
            pushTokenRepository.count();
        });
    }

    // User Entity Tests
    @Test
    void shouldCreateAndRetrieveUser() {
        // Given
        User user = new User("<EMAIL>", "password123");
        user.setFirstName("John");
        user.setLastName("Doe");
        user.setRole(Role.USER);

        // When
        User savedUser = userRepository.save(user);

        // Then
        assertThat(savedUser.getId()).isNotNull();
        assertThat(savedUser.getCreatedAt()).isNotNull();
        assertThat(savedUser.getUpdatedAt()).isNotNull();
        assertThat(savedUser.isEnabled()).isTrue();

        Optional<User> retrievedUser = userRepository.findById(savedUser.getId());
        assertThat(retrievedUser).isPresent();
        assertThat(retrievedUser.get().getEmail()).isEqualTo("<EMAIL>");
    }

    @Test
    void shouldEnforceEmailUniquenessConstraint() {
        // Given
        User user1 = new User("<EMAIL>", "password123");
        User user2 = new User("<EMAIL>", "password456");

        // When
        userRepository.save(user1);

        // Then
        assertThatThrownBy(() -> userRepository.save(user2))
                .isInstanceOf(DataIntegrityViolationException.class);
    }

    @Test
    void shouldFindUserByEmail() {
        // Given
        User user = new User("<EMAIL>", "password123");
        userRepository.save(user);

        // When
        Optional<User> found = userRepository.findByEmail("<EMAIL>");

        // Then
        assertThat(found).isPresent();
        assertThat(found.get().getEmail()).isEqualTo("<EMAIL>");
    }

    @Test
    void shouldCheckIfUserExistsByEmail() {
        // Given
        User user = new User("<EMAIL>", "password123");
        userRepository.save(user);

        // When & Then
        assertThat(userRepository.existsByEmail("<EMAIL>")).isTrue();
        assertThat(userRepository.existsByEmail("<EMAIL>")).isFalse();
    }

    @Test
    void shouldFindUsersWithFilters() {
        // Given
        User user1 = new User("<EMAIL>", "password123");
        user1.setRole(Role.ADMIN);
        user1.setEnabled(true);

        User user2 = new User("<EMAIL>", "password123");
        user2.setRole(Role.USER);
        user2.setEnabled(false);

        userRepository.save(user1);
        userRepository.save(user2);

        Pageable pageable = PageRequest.of(0, 10, Sort.by("email"));

        // When & Then
        Page<User> adminUsers = userRepository.findUsersWithFilters(null, null, Role.ADMIN, pageable);
        assertThat(adminUsers.getContent()).hasSize(1);
        assertThat(adminUsers.getContent().get(0).getRole()).isEqualTo(Role.ADMIN);

        Page<User> enabledUsers = userRepository.findUsersWithFilters(null, true, null, pageable);
        assertThat(enabledUsers.getContent()).hasSize(1);
        assertThat(enabledUsers.getContent().get(0).isEnabled()).isTrue();

        Page<User> emailFiltered = userRepository.findUsersWithFilters("admin", null, null, pageable);
        assertThat(emailFiltered.getContent()).hasSize(1);
        assertThat(emailFiltered.getContent().get(0).getEmail()).contains("admin");
    }

    @Test
    void shouldCountUsersByEnabledAndRole() {
        // Given
        User enabledUser = new User("<EMAIL>", "password123");
        enabledUser.setEnabled(true);
        enabledUser.setRole(Role.USER);

        User disabledAdmin = new User("<EMAIL>", "password123");
        disabledAdmin.setEnabled(false);
        disabledAdmin.setRole(Role.ADMIN);

        userRepository.save(enabledUser);
        userRepository.save(disabledAdmin);

        // When & Then
        assertThat(userRepository.countByEnabled(true)).isEqualTo(1);
        assertThat(userRepository.countByEnabled(false)).isEqualTo(1);
        assertThat(userRepository.countByRole(Role.USER)).isEqualTo(1);
        assertThat(userRepository.countByRole(Role.ADMIN)).isEqualTo(1);
    }

    // RefreshToken Entity Tests
    @Test
    void shouldCreateAndRetrieveRefreshToken() {
        // Given
        User user = new User("<EMAIL>", "password123");
        User savedUser = userRepository.save(user);

        RefreshToken token = new RefreshToken(
                "refresh-token-123",
                savedUser,
                SessionType.WEB,
                LocalDateTime.now().plusDays(7)
        );

        // When
        RefreshToken savedToken = refreshTokenRepository.save(token);

        // Then
        assertThat(savedToken.getId()).isNotNull();
        assertThat(savedToken.getCreatedAt()).isNotNull();
        assertThat(savedToken.isUsed()).isFalse();
        assertThat(savedToken.isValid()).isTrue();
    }

    @Test
    void shouldEnforceUserSessionTypeUniquenessConstraint() {
        // Given
        User user = new User("<EMAIL>", "password123");
        User savedUser = userRepository.save(user);

        RefreshToken token1 = new RefreshToken(
                "token-1",
                savedUser,
                SessionType.WEB,
                LocalDateTime.now().plusDays(7)
        );

        RefreshToken token2 = new RefreshToken(
                "token-2",
                savedUser,
                SessionType.WEB,
                LocalDateTime.now().plusDays(7)
        );

        // When
        refreshTokenRepository.save(token1);

        // Then
        assertThatThrownBy(() -> refreshTokenRepository.save(token2))
                .isInstanceOf(DataIntegrityViolationException.class);
    }

    @Test
    void shouldAllowMultipleSessionTypesPerUser() {
        // Given
        User user = new User("<EMAIL>", "password123");
        User savedUser = userRepository.save(user);

        RefreshToken webToken = new RefreshToken(
                "web-token",
                savedUser,
                SessionType.WEB,
                LocalDateTime.now().plusDays(7)
        );

        RefreshToken mobileToken = new RefreshToken(
                "mobile-token",
                savedUser,
                SessionType.MOBILE,
                LocalDateTime.now().plusDays(7)
        );

        // When & Then
        assertThatNoException().isThrownBy(() -> {
            refreshTokenRepository.save(webToken);
            refreshTokenRepository.save(mobileToken);
        });

        List<RefreshToken> userTokens = refreshTokenRepository.findByUser(savedUser);
        assertThat(userTokens).hasSize(2);
    }

    @Test
    void shouldFindTokenByUserAndSessionType() {
        // Given
        User user = new User("<EMAIL>", "password123");
        User savedUser = userRepository.save(user);

        RefreshToken token = new RefreshToken(
                "find-token",
                savedUser,
                SessionType.MOBILE,
                LocalDateTime.now().plusDays(7)
        );
        refreshTokenRepository.save(token);

        // When
        Optional<RefreshToken> found = refreshTokenRepository.findByUserAndSessionType(savedUser, SessionType.MOBILE);

        // Then
        assertThat(found).isPresent();
        assertThat(found.get().getSessionType()).isEqualTo(SessionType.MOBILE);
    }

    @Test
    void shouldFindValidTokens() {
        // Given
        User user = new User("<EMAIL>", "password123");
        User savedUser = userRepository.save(user);

        RefreshToken validToken = new RefreshToken(
                "valid-token",
                savedUser,
                SessionType.WEB,
                LocalDateTime.now().plusDays(7)
        );

        RefreshToken expiredToken = new RefreshToken(
                "expired-token",
                savedUser,
                SessionType.MOBILE,
                LocalDateTime.now().minusDays(1)
        );

        // Create another user for the used token to avoid constraint violation
        User user2 = new User("<EMAIL>", "password123");
        User savedUser2 = userRepository.save(user2);
        
        RefreshToken usedToken = new RefreshToken(
                "used-token",
                savedUser2,
                SessionType.WEB,
                LocalDateTime.now().plusDays(7)
        );
        usedToken.setUsed(true);

        refreshTokenRepository.save(validToken);
        refreshTokenRepository.save(expiredToken);
        refreshTokenRepository.save(usedToken);

        // When
        List<RefreshToken> validTokens = refreshTokenRepository.findValidTokensByUser(savedUser, LocalDateTime.now());

        // Then
        assertThat(validTokens).hasSize(1);
        assertThat(validTokens.get(0).getToken()).isEqualTo("valid-token");
    }

    @Test
    @Transactional
    void shouldMarkTokenAsUsed() {
        // Given
        User user = new User("<EMAIL>", "password123");
        User savedUser = userRepository.save(user);
        entityManager.flush();

        RefreshToken token = new RefreshToken(
                "mark-used-token",
                savedUser,
                SessionType.WEB,
                LocalDateTime.now().plusDays(7)
        );
        refreshTokenRepository.save(token);
        entityManager.flush();

        // When
        int updated = refreshTokenRepository.markTokenAsUsed("mark-used-token");
        entityManager.flush();
        entityManager.clear();

        // Then
        assertThat(updated).isEqualTo(1);
        Optional<RefreshToken> updatedToken = refreshTokenRepository.findByToken("mark-used-token");
        assertThat(updatedToken).isPresent();
        assertThat(updatedToken.get().isUsed()).isTrue();
    }

    @Test
    void shouldDeleteExpiredTokens() {
        // Given
        User user = new User("<EMAIL>", "password123");
        User savedUser = userRepository.save(user);

        RefreshToken expiredToken = new RefreshToken(
                "expired-cleanup",
                savedUser,
                SessionType.WEB,
                LocalDateTime.now().minusDays(1)
        );

        RefreshToken validToken = new RefreshToken(
                "valid-cleanup",
                savedUser,
                SessionType.MOBILE,
                LocalDateTime.now().plusDays(7)
        );

        refreshTokenRepository.save(expiredToken);
        refreshTokenRepository.save(validToken);

        // When
        int deleted = refreshTokenRepository.deleteExpiredTokens(LocalDateTime.now());

        // Then
        assertThat(deleted).isEqualTo(1);
        assertThat(refreshTokenRepository.findByToken("expired-cleanup")).isEmpty();
        assertThat(refreshTokenRepository.findByToken("valid-cleanup")).isPresent();
    }

    // PushToken Entity Tests
    @Test
    void shouldCreateAndRetrievePushToken() {
        // Given
        User user = new User("<EMAIL>", "password123");
        User savedUser = userRepository.save(user);

        PushToken pushToken = new PushToken(
                "push-token-123",
                savedUser,
                DeviceType.ANDROID,
                "device-123"
        );

        // When
        PushToken savedToken = pushTokenRepository.save(pushToken);

        // Then
        assertThat(savedToken.getId()).isNotNull();
        assertThat(savedToken.getCreatedAt()).isNotNull();
        assertThat(savedToken.getLastUsed()).isNull();
    }

    @Test
    void shouldEnforceOneToOneUserConstraint() {
        // Given
        User user = new User("<EMAIL>", "password123");
        User savedUser = userRepository.save(user);

        PushToken token1 = new PushToken("token-1", savedUser, DeviceType.ANDROID);
        PushToken token2 = new PushToken("token-2", savedUser, DeviceType.IOS);

        // When
        pushTokenRepository.save(token1);

        // Then
        assertThatThrownBy(() -> pushTokenRepository.save(token2))
                .isInstanceOf(DataIntegrityViolationException.class);
    }

    @Test
    void shouldFindPushTokenByUser() {
        // Given
        User user = new User("<EMAIL>", "password123");
        User savedUser = userRepository.save(user);

        PushToken pushToken = new PushToken("find-push-token", savedUser, DeviceType.IOS);
        pushTokenRepository.save(pushToken);

        // When
        Optional<PushToken> found = pushTokenRepository.findByUser(savedUser);

        // Then
        assertThat(found).isPresent();
        assertThat(found.get().getDeviceType()).isEqualTo(DeviceType.IOS);
    }

    @Test
    void shouldFindPushTokensByDeviceType() {
        // Given
        User user1 = new User("<EMAIL>", "password123");
        User user2 = new User("<EMAIL>", "password123");
        User savedUser1 = userRepository.save(user1);
        User savedUser2 = userRepository.save(user2);

        PushToken androidToken = new PushToken("android-token", savedUser1, DeviceType.ANDROID);
        PushToken iosToken = new PushToken("ios-token", savedUser2, DeviceType.IOS);

        pushTokenRepository.save(androidToken);
        pushTokenRepository.save(iosToken);

        // When
        List<PushToken> androidTokens = pushTokenRepository.findByDeviceType(DeviceType.ANDROID);
        List<PushToken> iosTokens = pushTokenRepository.findByDeviceType(DeviceType.IOS);

        // Then
        assertThat(androidTokens).hasSize(1);
        assertThat(iosTokens).hasSize(1);
        assertThat(androidTokens.get(0).getToken()).isEqualTo("android-token");
        assertThat(iosTokens.get(0).getToken()).isEqualTo("ios-token");
    }

    @Test
    @Transactional
    void shouldUpdateLastUsedTimestamp() {
        // Given
        User user = new User("<EMAIL>", "password123");
        User savedUser = userRepository.save(user);
        entityManager.flush();

        PushToken pushToken = new PushToken("last-used-token", savedUser, DeviceType.ANDROID);
        pushTokenRepository.save(pushToken);
        entityManager.flush();

        LocalDateTime lastUsed = LocalDateTime.now();

        // When
        int updated = pushTokenRepository.updateLastUsedByUser(savedUser, lastUsed);
        entityManager.flush();
        entityManager.clear();

        // Then
        assertThat(updated).isEqualTo(1);
        Optional<PushToken> updatedToken = pushTokenRepository.findByUser(savedUser);
        assertThat(updatedToken).isPresent();
        assertThat(updatedToken.get().getLastUsed()).isNotNull();
        assertThat(updatedToken.get().getLastUsed()).isEqualToIgnoringNanos(lastUsed);
    }

    @Test
    void shouldDeletePushTokenByUser() {
        // Given
        User user = new User("<EMAIL>", "password123");
        User savedUser = userRepository.save(user);

        PushToken pushToken = new PushToken("delete-token", savedUser, DeviceType.ANDROID);
        pushTokenRepository.save(pushToken);

        // When
        pushTokenRepository.deleteByUser(savedUser);

        // Then
        Optional<PushToken> deleted = pushTokenRepository.findByUser(savedUser);
        assertThat(deleted).isEmpty();
    }

    @Test
    void shouldFindStaleTokens() {
        // Given
        User user1 = new User("<EMAIL>", "password123");
        User user2 = new User("<EMAIL>", "password123");
        User savedUser1 = userRepository.save(user1);
        User savedUser2 = userRepository.save(user2);

        PushToken staleToken = new PushToken("stale-token", savedUser1, DeviceType.ANDROID);
        staleToken.setLastUsed(LocalDateTime.now().minusDays(30));

        PushToken recentToken = new PushToken("recent-token", savedUser2, DeviceType.IOS);
        recentToken.setLastUsed(LocalDateTime.now().minusHours(1));

        pushTokenRepository.save(staleToken);
        pushTokenRepository.save(recentToken);

        // When
        List<PushToken> staleTokens = pushTokenRepository.findStaleTokens(LocalDateTime.now().minusDays(7));

        // Then
        assertThat(staleTokens).hasSize(1);
        assertThat(staleTokens.get(0).getToken()).isEqualTo("stale-token");
    }

    @Test
    void shouldCountTokensByDeviceType() {
        // Given
        User user1 = new User("<EMAIL>", "password123");
        User user2 = new User("<EMAIL>", "password123");
        User savedUser1 = userRepository.save(user1);
        User savedUser2 = userRepository.save(user2);

        PushToken androidToken = new PushToken("count-android", savedUser1, DeviceType.ANDROID);
        PushToken iosToken = new PushToken("count-ios", savedUser2, DeviceType.IOS);

        pushTokenRepository.save(androidToken);
        pushTokenRepository.save(iosToken);

        // When & Then
        assertThat(pushTokenRepository.countByDeviceType(DeviceType.ANDROID)).isEqualTo(1);
        assertThat(pushTokenRepository.countByDeviceType(DeviceType.IOS)).isEqualTo(1);
        assertThat(pushTokenRepository.countByDeviceType(DeviceType.WEB)).isEqualTo(0);
    }

    // Cross-entity relationship tests
    @Test
    void shouldDeleteUserTokensManually() {
        // Given
        User user = new User("<EMAIL>", "password123");
        User savedUser = userRepository.save(user);

        RefreshToken refreshToken = new RefreshToken(
                "cascade-refresh",
                savedUser,
                SessionType.WEB,
                LocalDateTime.now().plusDays(7)
        );

        PushToken pushToken = new PushToken("cascade-push", savedUser, DeviceType.ANDROID);

        refreshTokenRepository.save(refreshToken);
        pushTokenRepository.save(pushToken);

        // When - Delete tokens first, then user (manual cascade)
        refreshTokenRepository.deleteByUser(savedUser);
        pushTokenRepository.deleteByUser(savedUser);
        userRepository.delete(savedUser);

        // Then
        assertThat(refreshTokenRepository.findByToken("cascade-refresh")).isEmpty();
        assertThat(pushTokenRepository.findByToken("cascade-push")).isEmpty();
        assertThat(userRepository.findById(savedUser.getId())).isEmpty();
    }
}
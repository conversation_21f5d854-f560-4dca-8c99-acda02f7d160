package co.com.gedsys.authentication.service;

import co.com.gedsys.authentication.config.JwtProperties;
import co.com.gedsys.authentication.entity.RefreshToken;
import co.com.gedsys.authentication.entity.Role;
import co.com.gedsys.authentication.entity.SessionType;
import co.com.gedsys.authentication.entity.User;
import co.com.gedsys.authentication.repository.RefreshTokenRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Comprehensive unit tests for RefreshTokenService single-use policy.
 * Tests token generation, validation, single-use enforcement, and cleanup.
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("Refresh Token Service Tests")
class RefreshTokenServiceTest {

    @Mock
    private RefreshTokenRepository refreshTokenRepository;

    @Mock
    private JwtProperties jwtProperties;

    @InjectMocks
    private RefreshTokenService refreshTokenService;

    private User testUser;
    private RefreshToken testRefreshToken;

    @BeforeEach
    void setUp() {
        testUser = new User();
        testUser.setId(1L);
        testUser.setEmail("<EMAIL>");
        testUser.setRole(Role.USER);
        testUser.setEnabled(true);

        testRefreshToken = new RefreshToken();
        testRefreshToken.setId(1L);
        testRefreshToken.setToken("test-refresh-token");
        testRefreshToken.setUser(testUser);
        testRefreshToken.setSessionType(SessionType.WEB);
        testRefreshToken.setExpiryDate(LocalDateTime.now().plusDays(7));
        testRefreshToken.setCreatedAt(LocalDateTime.now());
        testRefreshToken.setUsed(false);

        // Mock JWT properties
        when(jwtProperties.getRefreshTokenExpiration()).thenReturn(604800000L); // 7 days in milliseconds
    }

    @Nested
    @DisplayName("Token Generation Tests")
    class TokenGenerationTests {

        @Test
        @DisplayName("Should generate refresh token successfully")
        void shouldGenerateRefreshTokenSuccessfully() {
            // Given
            when(refreshTokenRepository.findByUserAndSessionType(testUser, SessionType.WEB))
                    .thenReturn(Optional.empty());
            when(refreshTokenRepository.save(any(RefreshToken.class))).thenReturn(testRefreshToken);

            // When
            RefreshToken generatedToken = refreshTokenService.generateRefreshToken(testUser, SessionType.WEB);

            // Then
            assertThat(generatedToken).isNotNull();
            assertThat(generatedToken.getUser()).isEqualTo(testUser);
            assertThat(generatedToken.getSessionType()).isEqualTo(SessionType.WEB);
            assertThat(generatedToken.getExpiryDate()).isAfter(LocalDateTime.now());
            assertThat(generatedToken.isUsed()).isFalse();

            verify(refreshTokenRepository).findByUserAndSessionType(testUser, SessionType.WEB);
            verify(refreshTokenRepository).save(any(RefreshToken.class));
        }

        @Test
        @DisplayName("Should invalidate existing token when generating new one")
        void shouldInvalidateExistingTokenWhenGeneratingNewOne() {
            // Given
            RefreshToken existingToken = new RefreshToken();
            existingToken.setToken("existing-token");
            existingToken.setUser(testUser);
            existingToken.setSessionType(SessionType.WEB);
            existingToken.setUsed(false);

            when(refreshTokenRepository.findByUserAndSessionType(testUser, SessionType.WEB))
                    .thenReturn(Optional.of(existingToken));
            when(refreshTokenRepository.save(any(RefreshToken.class))).thenReturn(testRefreshToken);

            // When
            refreshTokenService.generateRefreshToken(testUser, SessionType.WEB);

            // Then
            verify(refreshTokenRepository).save(argThat(token -> token.isUsed()));
            verify(refreshTokenRepository, times(2)).save(any(RefreshToken.class)); // Once for invalidation, once for new token
        }

        @Test
        @DisplayName("Should generate different tokens for different session types")
        void shouldGenerateDifferentTokensForDifferentSessionTypes() {
            // Given
            RefreshToken mobileToken = new RefreshToken();
            mobileToken.setToken("mobile-token");
            mobileToken.setSessionType(SessionType.MOBILE);

            when(refreshTokenRepository.findByUserAndSessionType(testUser, SessionType.WEB))
                    .thenReturn(Optional.empty());
            when(refreshTokenRepository.findByUserAndSessionType(testUser, SessionType.MOBILE))
                    .thenReturn(Optional.empty());
            when(refreshTokenRepository.save(any(RefreshToken.class)))
                    .thenReturn(testRefreshToken)
                    .thenReturn(mobileToken);

            // When
            RefreshToken webToken = refreshTokenService.generateRefreshToken(testUser, SessionType.WEB);
            RefreshToken generatedMobileToken = refreshTokenService.generateRefreshToken(testUser, SessionType.MOBILE);

            // Then
            assertThat(webToken.getSessionType()).isEqualTo(SessionType.WEB);
            assertThat(generatedMobileToken.getSessionType()).isEqualTo(SessionType.MOBILE);
            verify(refreshTokenRepository, times(2)).save(any(RefreshToken.class));
        }
    }

    @Nested
    @DisplayName("Token Validation and Consumption Tests")
    class TokenValidationAndConsumptionTests {

        @Test
        @DisplayName("Should validate and consume token successfully")
        void shouldValidateAndConsumeTokenSuccessfully() {
            // Given
            when(refreshTokenRepository.findValidTokenByToken(eq("test-refresh-token"), any(LocalDateTime.class)))
                    .thenReturn(Optional.of(testRefreshToken));
            when(refreshTokenRepository.save(any(RefreshToken.class))).thenReturn(testRefreshToken);

            // When
            RefreshToken validatedToken = refreshTokenService.validateAndConsumeToken("test-refresh-token");

            // Then
            assertThat(validatedToken).isNotNull();
            assertThat(validatedToken.getUser()).isEqualTo(testUser);
            verify(refreshTokenRepository).findValidTokenByToken(eq("test-refresh-token"), any(LocalDateTime.class));
            verify(refreshTokenRepository).save(argThat(token -> token.isUsed()));
        }

        @Test
        @DisplayName("Should throw exception for invalid token")
        void shouldThrowExceptionForInvalidToken() {
            // Given
            when(refreshTokenRepository.findValidTokenByToken(eq("invalid-token"), any(LocalDateTime.class)))
                    .thenReturn(Optional.empty());

            // When & Then
            assertThatThrownBy(() -> refreshTokenService.validateAndConsumeToken("invalid-token"))
                    .isInstanceOf(IllegalArgumentException.class)
                    .hasMessageContaining("Invalid or expired refresh token");

            verify(refreshTokenRepository, never()).save(any(RefreshToken.class));
        }

        @Test
        @DisplayName("Should throw exception for expired token")
        void shouldThrowExceptionForExpiredToken() {
            // Given
            testRefreshToken.setExpiryDate(LocalDateTime.now().minusDays(1)); // Expired token
            when(refreshTokenRepository.findValidTokenByToken(eq("expired-token"), any(LocalDateTime.class)))
                    .thenReturn(Optional.empty());

            // When & Then
            assertThatThrownBy(() -> refreshTokenService.validateAndConsumeToken("expired-token"))
                    .isInstanceOf(IllegalArgumentException.class)
                    .hasMessageContaining("Invalid or expired refresh token");
        }

        @Test
        @DisplayName("Should mark token as used after validation")
        void shouldMarkTokenAsUsedAfterValidation() {
            // Given
            when(refreshTokenRepository.findValidTokenByToken(eq("test-refresh-token"), any(LocalDateTime.class)))
                    .thenReturn(Optional.of(testRefreshToken));
            when(refreshTokenRepository.save(any(RefreshToken.class))).thenReturn(testRefreshToken);

            // When
            refreshTokenService.validateAndConsumeToken("test-refresh-token");

            // Then
            verify(refreshTokenRepository).save(argThat(token -> 
                token.isUsed()
            ));
        }
    }

    @Nested
    @DisplayName("Token Query Tests")
    class TokenQueryTests {

        @Test
        @DisplayName("Should find token by value")
        void shouldFindTokenByValue() {
            // Given
            when(refreshTokenRepository.findByToken("test-refresh-token"))
                    .thenReturn(Optional.of(testRefreshToken));

            // When
            Optional<RefreshToken> foundToken = refreshTokenService.findByToken("test-refresh-token");

            // Then
            assertThat(foundToken).isPresent().contains(testRefreshToken);
            verify(refreshTokenRepository).findByToken("test-refresh-token");
        }

        @Test
        @DisplayName("Should return empty when token not found")
        void shouldReturnEmptyWhenTokenNotFound() {
            // Given
            when(refreshTokenRepository.findByToken("nonexistent-token"))
                    .thenReturn(Optional.empty());

            // When
            Optional<RefreshToken> foundToken = refreshTokenService.findByToken("nonexistent-token");

            // Then
            assertThat(foundToken).isEmpty();
        }

        @Test
        @DisplayName("Should check if token is valid")
        void shouldCheckIfTokenIsValid() {
            // Given
            when(refreshTokenRepository.findValidTokenByToken(eq("valid-token"), any(LocalDateTime.class)))
                    .thenReturn(Optional.of(testRefreshToken));
            when(refreshTokenRepository.findValidTokenByToken(eq("invalid-token"), any(LocalDateTime.class)))
                    .thenReturn(Optional.empty());

            // When & Then
            assertThat(refreshTokenService.isTokenValid("valid-token")).isTrue();
            assertThat(refreshTokenService.isTokenValid("invalid-token")).isFalse();
        }

        @Test
        @DisplayName("Should get valid tokens for user")
        void shouldGetValidTokensForUser() {
            // Given
            List<RefreshToken> validTokens = Arrays.asList(testRefreshToken);
            when(refreshTokenRepository.findValidTokensByUser(eq(testUser), any(LocalDateTime.class)))
                    .thenReturn(validTokens);

            // When
            List<RefreshToken> result = refreshTokenService.getValidTokensForUser(testUser);

            // Then
            assertThat(result).hasSize(1).contains(testRefreshToken);
            verify(refreshTokenRepository).findValidTokensByUser(eq(testUser), any(LocalDateTime.class));
        }

        @Test
        @DisplayName("Should check if user has valid token for session type")
        void shouldCheckIfUserHasValidTokenForSessionType() {
            // Given
            when(refreshTokenRepository.hasValidTokenForSessionType(eq(testUser), eq(SessionType.WEB), any(LocalDateTime.class)))
                    .thenReturn(true);
            when(refreshTokenRepository.hasValidTokenForSessionType(eq(testUser), eq(SessionType.MOBILE), any(LocalDateTime.class)))
                    .thenReturn(false);

            // When & Then
            assertThat(refreshTokenService.hasValidTokenForSessionType(testUser, SessionType.WEB)).isTrue();
            assertThat(refreshTokenService.hasValidTokenForSessionType(testUser, SessionType.MOBILE)).isFalse();
        }

        @Test
        @DisplayName("Should get valid token count for user")
        void shouldGetValidTokenCountForUser() {
            // Given
            when(refreshTokenRepository.countValidTokensByUser(eq(testUser), any(LocalDateTime.class)))
                    .thenReturn(2L);

            // When
            long count = refreshTokenService.getValidTokenCountForUser(testUser);

            // Then
            assertThat(count).isEqualTo(2L);
            verify(refreshTokenRepository).countValidTokensByUser(eq(testUser), any(LocalDateTime.class));
        }
    }

    @Nested
    @DisplayName("Token Invalidation Tests")
    class TokenInvalidationTests {

        @Test
        @DisplayName("Should invalidate all user tokens")
        void shouldInvalidateAllUserTokens() {
            // Given
            RefreshToken token1 = new RefreshToken();
            token1.setUsed(false);
            RefreshToken token2 = new RefreshToken();
            token2.setUsed(false);
            List<RefreshToken> userTokens = Arrays.asList(token1, token2);

            when(refreshTokenRepository.findByUser(testUser)).thenReturn(userTokens);
            when(refreshTokenRepository.save(any(RefreshToken.class))).thenReturn(token1, token2);

            // When
            refreshTokenService.invalidateAllUserTokens(testUser);

            // Then
            verify(refreshTokenRepository).findByUser(testUser);
            verify(refreshTokenRepository, times(2)).save(argThat(token -> token.isUsed()));
        }

        @Test
        @DisplayName("Should invalidate tokens for specific session type")
        void shouldInvalidateTokensForSpecificSessionType() {
            // Given
            // When
            refreshTokenService.invalidateTokensForSessionType(testUser, SessionType.WEB);

            // Then
            verify(refreshTokenRepository).markTokensAsUsedByUserAndSessionType(testUser, SessionType.WEB);
        }
    }

    @Nested
    @DisplayName("Token Deletion Tests")
    class TokenDeletionTests {

        @Test
        @DisplayName("Should delete specific token")
        void shouldDeleteSpecificToken() {
            // When
            refreshTokenService.deleteToken(testRefreshToken);

            // Then
            verify(refreshTokenRepository).delete(testRefreshToken);
        }

        @Test
        @DisplayName("Should delete all user tokens")
        void shouldDeleteAllUserTokens() {
            // When
            refreshTokenService.deleteAllUserTokens(testUser);

            // Then
            verify(refreshTokenRepository).deleteByUser(testUser);
        }

        @Test
        @DisplayName("Should delete tokens for specific session type")
        void shouldDeleteTokensForSpecificSessionType() {
            // When
            refreshTokenService.deleteTokensForSessionType(testUser, SessionType.MOBILE);

            // Then
            verify(refreshTokenRepository).deleteByUserAndSessionType(testUser, SessionType.MOBILE);
        }
    }

    @Nested
    @DisplayName("Token Cleanup Tests")
    class TokenCleanupTests {

        @Test
        @DisplayName("Should cleanup expired and used tokens")
        void shouldCleanupExpiredAndUsedTokens() {
            // Given
            when(refreshTokenRepository.deleteExpiredTokens(any(LocalDateTime.class))).thenReturn(5);
            when(refreshTokenRepository.deleteUsedTokens()).thenReturn(3);

            // When
            refreshTokenService.cleanupExpiredAndUsedTokens();

            // Then
            verify(refreshTokenRepository).deleteExpiredTokens(any(LocalDateTime.class));
            verify(refreshTokenRepository).deleteUsedTokens();
        }

        @Test
        @DisplayName("Should handle cleanup with no tokens to delete")
        void shouldHandleCleanupWithNoTokensToDelete() {
            // Given
            when(refreshTokenRepository.deleteExpiredTokens(any(LocalDateTime.class))).thenReturn(0);
            when(refreshTokenRepository.deleteUsedTokens()).thenReturn(0);

            // When
            refreshTokenService.cleanupExpiredAndUsedTokens();

            // Then
            verify(refreshTokenRepository).deleteExpiredTokens(any(LocalDateTime.class));
            verify(refreshTokenRepository).deleteUsedTokens();
        }
    }

    @Nested
    @DisplayName("Single-Use Policy Tests")
    class SingleUsePolicyTests {

        @Test
        @DisplayName("Should enforce single-use policy by marking token as used")
        void shouldEnforceSingleUsePolicyByMarkingTokenAsUsed() {
            // Given
            RefreshToken freshToken = new RefreshToken();
            freshToken.setToken("fresh-token");
            freshToken.setUser(testUser);
            freshToken.setUsed(false);

            when(refreshTokenRepository.findValidTokenByToken(eq("fresh-token"), any(LocalDateTime.class)))
                    .thenReturn(Optional.of(freshToken));
            when(refreshTokenRepository.save(any(RefreshToken.class))).thenReturn(freshToken);

            // When
            RefreshToken validatedToken = refreshTokenService.validateAndConsumeToken("fresh-token");

            // Then
            assertThat(validatedToken).isNotNull();
            verify(refreshTokenRepository).save(argThat(token -> 
                token.isUsed()
            ));
        }

        @Test
        @DisplayName("Should prevent reuse of consumed token")
        void shouldPreventReuseOfConsumedToken() {
            // Given
            RefreshToken usedToken = new RefreshToken();
            usedToken.setToken("used-token");
            usedToken.setUsed(true);

            when(refreshTokenRepository.findValidTokenByToken(eq("used-token"), any(LocalDateTime.class)))
                    .thenReturn(Optional.empty()); // Used tokens are not returned by findValidTokenByToken

            // When & Then
            assertThatThrownBy(() -> refreshTokenService.validateAndConsumeToken("used-token"))
                    .isInstanceOf(IllegalArgumentException.class)
                    .hasMessageContaining("Invalid or expired refresh token");
        }

        @Test
        @DisplayName("Should allow only one active token per user per session type")
        void shouldAllowOnlyOneActiveTokenPerUserPerSessionType() {
            // Given
            RefreshToken existingWebToken = new RefreshToken();
            existingWebToken.setToken("existing-web-token");
            existingWebToken.setUser(testUser);
            existingWebToken.setSessionType(SessionType.WEB);
            existingWebToken.setUsed(false);

            when(refreshTokenRepository.findByUserAndSessionType(testUser, SessionType.WEB))
                    .thenReturn(Optional.of(existingWebToken));
            when(refreshTokenRepository.save(any(RefreshToken.class))).thenReturn(testRefreshToken);

            // When
            refreshTokenService.generateRefreshToken(testUser, SessionType.WEB);

            // Then
            // Verify existing token is marked as used
            verify(refreshTokenRepository).save(argThat(token -> 
                token.getToken().equals("existing-web-token") && token.isUsed()
            ));
            // Verify new token is created
            verify(refreshTokenRepository).save(argThat(token -> 
                !token.isUsed() && token.getSessionType() == SessionType.WEB
            ));
        }
    }

    @Nested
    @DisplayName("Edge Cases and Error Handling")
    class EdgeCasesAndErrorHandlingTests {

        @Test
        @DisplayName("Should handle null token value gracefully")
        void shouldHandleNullTokenValueGracefully() {
            // Given
            when(refreshTokenRepository.findValidTokenByToken(eq(null), any(LocalDateTime.class)))
                    .thenReturn(Optional.empty());

            // When & Then
            assertThatThrownBy(() -> refreshTokenService.validateAndConsumeToken(null))
                    .isInstanceOf(IllegalArgumentException.class);
        }

        @Test
        @DisplayName("Should handle empty token value gracefully")
        void shouldHandleEmptyTokenValueGracefully() {
            // Given
            when(refreshTokenRepository.findValidTokenByToken(eq(""), any(LocalDateTime.class)))
                    .thenReturn(Optional.empty());

            // When & Then
            assertThatThrownBy(() -> refreshTokenService.validateAndConsumeToken(""))
                    .isInstanceOf(IllegalArgumentException.class);
        }

        @Test
        @DisplayName("Should handle repository exceptions during token generation")
        void shouldHandleRepositoryExceptionsDuringTokenGeneration() {
            // Given
            when(refreshTokenRepository.findByUserAndSessionType(testUser, SessionType.WEB))
                    .thenThrow(new RuntimeException("Database error"));

            // When & Then
            assertThatThrownBy(() -> refreshTokenService.generateRefreshToken(testUser, SessionType.WEB))
                    .isInstanceOf(RuntimeException.class)
                    .hasMessageContaining("Database error");
        }

        @Test
        @DisplayName("Should handle user with null ID")
        void shouldHandleUserWithNullId() {
            // Given
            User userWithNullId = new User();
            userWithNullId.setId(null);
            userWithNullId.setEmail("<EMAIL>");

            when(refreshTokenRepository.findByUserAndSessionType(userWithNullId, SessionType.WEB))
                    .thenReturn(Optional.empty());
            when(refreshTokenRepository.save(any(RefreshToken.class))).thenReturn(testRefreshToken);

            // When
            RefreshToken result = refreshTokenService.generateRefreshToken(userWithNullId, SessionType.WEB);

            // Then
            assertThat(result).isNotNull();
            verify(refreshTokenRepository).save(any(RefreshToken.class));
        }
    }
}
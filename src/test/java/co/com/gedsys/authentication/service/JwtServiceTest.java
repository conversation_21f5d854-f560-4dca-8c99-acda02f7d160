package co.com.gedsys.authentication.service;

import co.com.gedsys.authentication.config.JwtProperties;
import co.com.gedsys.authentication.entity.Role;
import co.com.gedsys.authentication.entity.SessionType;
import co.com.gedsys.authentication.entity.User;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.MalformedJwtException;
import io.jsonwebtoken.security.SecurityException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Date;

import static org.assertj.core.api.Assertions.*;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive unit tests for JwtService functionality.
 * Tests token generation, validation, claim extraction, and edge cases.
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("JWT Service Tests")
class JwtServiceTest {

    private JwtService jwtService;
    private JwtProperties jwtProperties;
    private User testUser;

    @BeforeEach
    void setUp() {
        // Setup JWT properties with test values
        jwtProperties = new JwtProperties();
        jwtProperties.setSecret("test-secret-key-that-is-long-enough-for-hmac-sha256-algorithm");
        jwtProperties.setAccessTokenExpiration(3600000L); // 1 hour
        jwtProperties.setRefreshTokenExpiration(604800000L); // 7 days
        
        // Configure mobile and web specific settings
        JwtProperties.Mobile mobile = new JwtProperties.Mobile();
        mobile.setAccessTokenExpiration(7200000L); // 2 hours
        jwtProperties.setMobile(mobile);
        
        JwtProperties.Web web = new JwtProperties.Web();
        web.setAccessTokenExpiration(3600000L); // 1 hour
        jwtProperties.setWeb(web);
        
        jwtService = new JwtService(jwtProperties);
        
        // Setup test user
        testUser = new User();
        testUser.setId(1L);
        testUser.setEmail("<EMAIL>");
        testUser.setPassword("password123");
        testUser.setFirstName("Test");
        testUser.setLastName("User");
        testUser.setRole(Role.USER);
        testUser.setEnabled(true);
        testUser.setCreatedAt(LocalDateTime.now());
        testUser.setUpdatedAt(LocalDateTime.now());
    }

    @Nested
    @DisplayName("Token Generation Tests")
    class TokenGenerationTests {

        @Test
        @DisplayName("Should generate valid access token for mobile session")
        void shouldGenerateValidAccessTokenForMobile() {
            // When
            String token = jwtService.generateAccessToken(testUser, SessionType.MOBILE);
            
            // Then
            assertThat(token).isNotNull().isNotEmpty();
            assertThat(jwtService.isTokenValid(token)).isTrue();
            assertThat(jwtService.extractUsername(token)).isEqualTo(testUser.getEmail());
            assertThat(jwtService.extractUserId(token)).isEqualTo(testUser.getId());
            assertThat(jwtService.extractRole(token)).isEqualTo(testUser.getRole().name());
            assertThat(jwtService.extractSessionType(token)).isEqualTo(SessionType.MOBILE);
            assertThat(jwtService.isRefreshToken(token)).isFalse();
        }

        @Test
        @DisplayName("Should generate valid access token for web session")
        void shouldGenerateValidAccessTokenForWeb() {
            // When
            String token = jwtService.generateAccessToken(testUser, SessionType.WEB);
            
            // Then
            assertThat(token).isNotNull().isNotEmpty();
            assertThat(jwtService.isTokenValid(token)).isTrue();
            assertThat(jwtService.extractUsername(token)).isEqualTo(testUser.getEmail());
            assertThat(jwtService.extractUserId(token)).isEqualTo(testUser.getId());
            assertThat(jwtService.extractRole(token)).isEqualTo(testUser.getRole().name());
            assertThat(jwtService.extractSessionType(token)).isEqualTo(SessionType.WEB);
            assertThat(jwtService.isRefreshToken(token)).isFalse();
        }

        @Test
        @DisplayName("Should generate valid refresh token")
        void shouldGenerateValidRefreshToken() {
            // When
            String refreshToken = jwtService.generateRefreshToken(testUser, SessionType.MOBILE);
            
            // Then
            assertThat(refreshToken).isNotNull().isNotEmpty();
            assertThat(jwtService.isTokenValid(refreshToken)).isTrue();
            assertThat(jwtService.extractUsername(refreshToken)).isEqualTo(testUser.getEmail());
            assertThat(jwtService.extractUserId(refreshToken)).isEqualTo(testUser.getId());
            assertThat(jwtService.extractSessionType(refreshToken)).isEqualTo(SessionType.MOBILE);
            assertThat(jwtService.isRefreshToken(refreshToken)).isTrue();
        }

        @Test
        @DisplayName("Should generate different tokens for different session types")
        void shouldGenerateDifferentTokensForDifferentSessionTypes() {
            // When
            String mobileToken = jwtService.generateAccessToken(testUser, SessionType.MOBILE);
            String webToken = jwtService.generateAccessToken(testUser, SessionType.WEB);
            
            // Then
            assertThat(mobileToken).isNotEqualTo(webToken);
            assertThat(jwtService.extractSessionType(mobileToken)).isEqualTo(SessionType.MOBILE);
            assertThat(jwtService.extractSessionType(webToken)).isEqualTo(SessionType.WEB);
        }

        @Test
        @DisplayName("Should generate different tokens for different users")
        void shouldGenerateDifferentTokensForDifferentUsers() {
            // Given
            User anotherUser = new User();
            anotherUser.setId(2L);
            anotherUser.setEmail("<EMAIL>");
            anotherUser.setRole(Role.ADMIN);
            anotherUser.setEnabled(true);
            
            // When
            String token1 = jwtService.generateAccessToken(testUser, SessionType.WEB);
            String token2 = jwtService.generateAccessToken(anotherUser, SessionType.WEB);
            
            // Then
            assertThat(token1).isNotEqualTo(token2);
            assertThat(jwtService.extractUserId(token1)).isEqualTo(testUser.getId());
            assertThat(jwtService.extractUserId(token2)).isEqualTo(anotherUser.getId());
            assertThat(jwtService.extractRole(token1)).isEqualTo(Role.USER.name());
            assertThat(jwtService.extractRole(token2)).isEqualTo(Role.ADMIN.name());
        }
    }

    @Nested
    @DisplayName("Token Validation Tests")
    class TokenValidationTests {

        @Test
        @DisplayName("Should validate token successfully with correct user")
        void shouldValidateTokenSuccessfullyWithCorrectUser() {
            // Given
            String token = jwtService.generateAccessToken(testUser, SessionType.WEB);
            
            // When & Then
            assertThat(jwtService.validateToken(token, testUser)).isTrue();
        }

        @Test
        @DisplayName("Should reject token validation with wrong user")
        void shouldRejectTokenValidationWithWrongUser() {
            // Given
            String token = jwtService.generateAccessToken(testUser, SessionType.WEB);
            User wrongUser = new User();
            wrongUser.setId(2L);
            wrongUser.setEmail("<EMAIL>");
            wrongUser.setEnabled(true);
            
            // When & Then
            assertThat(jwtService.validateToken(token, wrongUser)).isFalse();
        }

        @Test
        @DisplayName("Should reject token validation for disabled user")
        void shouldRejectTokenValidationForDisabledUser() {
            // Given
            String token = jwtService.generateAccessToken(testUser, SessionType.WEB);
            testUser.setEnabled(false);
            
            // When & Then
            assertThat(jwtService.validateToken(token, testUser)).isFalse();
        }

        @Test
        @DisplayName("Should validate token structure without user validation")
        void shouldValidateTokenStructureWithoutUserValidation() {
            // Given
            String token = jwtService.generateAccessToken(testUser, SessionType.WEB);
            
            // When & Then
            assertThat(jwtService.isTokenValid(token)).isTrue();
        }

        @Test
        @DisplayName("Should reject malformed token")
        void shouldRejectMalformedToken() {
            // Given
            String malformedToken = "invalid.token.structure";
            
            // When & Then
            assertThat(jwtService.isTokenValid(malformedToken)).isFalse();
            assertThat(jwtService.validateToken(malformedToken, testUser)).isFalse();
        }

        @Test
        @DisplayName("Should reject empty or null token")
        void shouldRejectEmptyOrNullToken() {
            // When & Then
            assertThat(jwtService.isTokenValid(null)).isFalse();
            assertThat(jwtService.isTokenValid("")).isFalse();
            assertThat(jwtService.validateToken(null, testUser)).isFalse();
            assertThat(jwtService.validateToken("", testUser)).isFalse();
        }
    }

    @Nested
    @DisplayName("Claim Extraction Tests")
    class ClaimExtractionTests {

        @Test
        @DisplayName("Should extract username correctly")
        void shouldExtractUsernameCorrectly() {
            // Given
            String token = jwtService.generateAccessToken(testUser, SessionType.WEB);
            
            // When
            String extractedUsername = jwtService.extractUsername(token);
            
            // Then
            assertThat(extractedUsername).isEqualTo(testUser.getEmail());
        }

        @Test
        @DisplayName("Should extract user ID correctly")
        void shouldExtractUserIdCorrectly() {
            // Given
            String token = jwtService.generateAccessToken(testUser, SessionType.WEB);
            
            // When
            Long extractedUserId = jwtService.extractUserId(token);
            
            // Then
            assertThat(extractedUserId).isEqualTo(testUser.getId());
        }

        @Test
        @DisplayName("Should extract role correctly")
        void shouldExtractRoleCorrectly() {
            // Given
            testUser.setRole(Role.ADMIN);
            String token = jwtService.generateAccessToken(testUser, SessionType.WEB);
            
            // When
            String extractedRole = jwtService.extractRole(token);
            
            // Then
            assertThat(extractedRole).isEqualTo(Role.ADMIN.name());
        }

        @Test
        @DisplayName("Should extract session type correctly")
        void shouldExtractSessionTypeCorrectly() {
            // Given
            String mobileToken = jwtService.generateAccessToken(testUser, SessionType.MOBILE);
            String webToken = jwtService.generateAccessToken(testUser, SessionType.WEB);
            
            // When & Then
            assertThat(jwtService.extractSessionType(mobileToken)).isEqualTo(SessionType.MOBILE);
            assertThat(jwtService.extractSessionType(webToken)).isEqualTo(SessionType.WEB);
        }

        @Test
        @DisplayName("Should extract expiration date correctly")
        void shouldExtractExpirationDateCorrectly() {
            // Given
            String token = jwtService.generateAccessToken(testUser, SessionType.WEB);
            
            // When
            Date expiration = jwtService.extractExpiration(token);
            
            // Then
            assertThat(expiration).isNotNull();
            assertThat(expiration).isAfter(new Date());
        }

        @Test
        @DisplayName("Should throw exception when extracting claims from malformed token")
        void shouldThrowExceptionWhenExtractingClaimsFromMalformedToken() {
            // Given
            String malformedToken = "invalid.token.structure";
            
            // When & Then
            assertThatThrownBy(() -> jwtService.extractUsername(malformedToken))
                    .isInstanceOf(MalformedJwtException.class);
        }
    }

    @Nested
    @DisplayName("Token Expiration Tests")
    class TokenExpirationTests {

        @Test
        @DisplayName("Should detect non-expired token")
        void shouldDetectNonExpiredToken() {
            // Given
            String token = jwtService.generateAccessToken(testUser, SessionType.WEB);
            
            // When & Then
            assertThat(jwtService.isTokenExpired(token)).isFalse();
        }

        @Test
        @DisplayName("Should get remaining expiration time")
        void shouldGetRemainingExpirationTime() {
            // Given
            String token = jwtService.generateAccessToken(testUser, SessionType.WEB);
            
            // When
            long remainingTime = jwtService.getTokenExpirationTime(token);
            
            // Then
            assertThat(remainingTime).isGreaterThan(0);
            assertThat(remainingTime).isLessThanOrEqualTo(3600); // 1 hour in seconds
        }

        @Test
        @DisplayName("Should return zero expiration time for malformed token")
        void shouldReturnZeroExpirationTimeForMalformedToken() {
            // Given
            String malformedToken = "invalid.token.structure";
            
            // When
            long remainingTime = jwtService.getTokenExpirationTime(malformedToken);
            
            // Then
            assertThat(remainingTime).isEqualTo(0);
        }

        @Test
        @DisplayName("Should handle expired token gracefully")
        void shouldHandleExpiredTokenGracefully() {
            // Given - Create JWT properties with very short expiration
            JwtProperties shortExpirationProperties = new JwtProperties();
            shortExpirationProperties.setSecret("test-secret-key-that-is-long-enough-for-hmac-sha256-algorithm");
            shortExpirationProperties.setAccessTokenExpiration(1L); // 1 millisecond
            shortExpirationProperties.setRefreshTokenExpiration(1L);
            
            JwtProperties.Web web = new JwtProperties.Web();
            web.setAccessTokenExpiration(1L);
            shortExpirationProperties.setWeb(web);
            
            JwtService shortExpirationJwtService = new JwtService(shortExpirationProperties);
            
            // When
            String token = shortExpirationJwtService.generateAccessToken(testUser, SessionType.WEB);
            
            // Wait for token to expire
            try {
                Thread.sleep(10);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            
            // Then
            assertThat(shortExpirationJwtService.isTokenExpired(token)).isTrue();
            assertThat(shortExpirationJwtService.isTokenValid(token)).isFalse();
            assertThat(shortExpirationJwtService.validateToken(token, testUser)).isFalse();
            assertThat(shortExpirationJwtService.getTokenExpirationTime(token)).isEqualTo(0);
        }
    }

    @Nested
    @DisplayName("Refresh Token Tests")
    class RefreshTokenTests {

        @Test
        @DisplayName("Should identify refresh token correctly")
        void shouldIdentifyRefreshTokenCorrectly() {
            // Given
            String accessToken = jwtService.generateAccessToken(testUser, SessionType.WEB);
            String refreshToken = jwtService.generateRefreshToken(testUser, SessionType.WEB);
            
            // When & Then
            assertThat(jwtService.isRefreshToken(accessToken)).isFalse();
            assertThat(jwtService.isRefreshToken(refreshToken)).isTrue();
        }

        @Test
        @DisplayName("Should handle malformed token when checking refresh token type")
        void shouldHandleMalformedTokenWhenCheckingRefreshTokenType() {
            // Given
            String malformedToken = "invalid.token.structure";
            
            // When & Then
            assertThat(jwtService.isRefreshToken(malformedToken)).isFalse();
        }
    }

    @Nested
    @DisplayName("Session Type Specific Tests")
    class SessionTypeSpecificTests {

        @Test
        @DisplayName("Should use different expiration times for mobile and web sessions")
        void shouldUseDifferentExpirationTimesForMobileAndWebSessions() {
            // Given
            String mobileToken = jwtService.generateAccessToken(testUser, SessionType.MOBILE);
            String webToken = jwtService.generateAccessToken(testUser, SessionType.WEB);
            
            // When
            long mobileExpirationTime = jwtService.getTokenExpirationTime(mobileToken);
            long webExpirationTime = jwtService.getTokenExpirationTime(webToken);
            
            // Then
            // Mobile should have longer expiration (2 hours vs 1 hour)
            assertThat(mobileExpirationTime).isGreaterThan(webExpirationTime);
        }
    }

    @Nested
    @DisplayName("Edge Cases and Error Handling")
    class EdgeCasesAndErrorHandlingTests {

        @Test
        @DisplayName("Should handle token with tampered signature")
        void shouldHandleTokenWithTamperedSignature() {
            // Given
            String validToken = jwtService.generateAccessToken(testUser, SessionType.WEB);
            String tamperedToken = validToken.substring(0, validToken.length() - 5) + "XXXXX";
            
            // When & Then
            assertThat(jwtService.isTokenValid(tamperedToken)).isFalse();
            assertThat(jwtService.validateToken(tamperedToken, testUser)).isFalse();
            
            assertThatThrownBy(() -> jwtService.extractUsername(tamperedToken))
                    .isInstanceOf(SecurityException.class);
        }

        @Test
        @DisplayName("Should handle null user in validation")
        void shouldHandleNullUserInValidation() {
            // Given
            String token = jwtService.generateAccessToken(testUser, SessionType.WEB);
            
            // When & Then
            assertThat(jwtService.validateToken(token, null)).isFalse();
        }

        @Test
        @DisplayName("Should handle user with null ID")
        void shouldHandleUserWithNullId() {
            // Given
            User userWithNullId = new User();
            userWithNullId.setId(null);
            userWithNullId.setEmail("<EMAIL>");
            userWithNullId.setEnabled(true);
            
            String token = jwtService.generateAccessToken(testUser, SessionType.WEB);
            
            // When & Then
            assertThat(jwtService.validateToken(token, userWithNullId)).isFalse();
        }

        @Test
        @DisplayName("Should handle user with null email")
        void shouldHandleUserWithNullEmail() {
            // Given
            User userWithNullEmail = new User();
            userWithNullEmail.setId(1L);
            userWithNullEmail.setEmail(null);
            userWithNullEmail.setEnabled(true);
            
            String token = jwtService.generateAccessToken(testUser, SessionType.WEB);
            
            // When & Then
            assertThat(jwtService.validateToken(token, userWithNullEmail)).isFalse();
        }
    }
}
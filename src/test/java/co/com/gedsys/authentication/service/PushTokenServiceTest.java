package co.com.gedsys.authentication.service;

import co.com.gedsys.authentication.entity.DeviceType;
import co.com.gedsys.authentication.entity.PushToken;
import co.com.gedsys.authentication.entity.Role;
import co.com.gedsys.authentication.entity.User;
import co.com.gedsys.authentication.repository.PushTokenRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Comprehensive unit tests for PushTokenService device management.
 * Tests single token per user policy, device management, and cleanup operations.
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("Push Token Service Tests")
class PushTokenServiceTest {

    @Mock
    private PushTokenRepository pushTokenRepository;

    @InjectMocks
    private PushTokenService pushTokenService;

    private User testUser;
    private PushToken testPushToken;

    @BeforeEach
    void setUp() {
        testUser = new User();
        testUser.setId(1L);
        testUser.setEmail("<EMAIL>");
        testUser.setRole(Role.USER);
        testUser.setEnabled(true);

        testPushToken = new PushToken();
        testPushToken.setId(1L);
        testPushToken.setToken("test-push-token-value");
        testPushToken.setUser(testUser);
        testPushToken.setDeviceType(DeviceType.ANDROID);
        testPushToken.setDeviceId("device-123");
        testPushToken.setCreatedAt(LocalDateTime.now());
        testPushToken.setLastUsed(LocalDateTime.now());
    }

    @Nested
    @DisplayName("Token Registration Tests")
    class TokenRegistrationTests {

        @Test
        @DisplayName("Should register push token successfully for new user")
        void shouldRegisterPushTokenSuccessfullyForNewUser() {
            // Given
            when(pushTokenRepository.findByUser(testUser)).thenReturn(Optional.empty());
            when(pushTokenRepository.save(any(PushToken.class))).thenReturn(testPushToken);

            // When
            PushToken registeredToken = pushTokenService.registerPushToken(
                testUser, "new-push-token", DeviceType.ANDROID, "device-123"
            );

            // Then
            assertThat(registeredToken).isNotNull();
            assertThat(registeredToken.getUser()).isEqualTo(testUser);
            assertThat(registeredToken.getDeviceType()).isEqualTo(DeviceType.ANDROID);
            assertThat(registeredToken.getDeviceId()).isEqualTo("device-123");

            verify(pushTokenRepository).findByUser(testUser);
            verify(pushTokenRepository, never()).delete(any(PushToken.class));
            verify(pushTokenRepository).save(argThat(token -> 
                token.getToken().equals("new-push-token") &&
                token.getLastUsed() != null
            ));
        }

        @Test
        @DisplayName("Should replace existing push token when registering new one")
        void shouldReplaceExistingPushTokenWhenRegisteringNewOne() {
            // Given
            PushToken existingToken = new PushToken();
            existingToken.setToken("old-push-token");
            existingToken.setUser(testUser);
            existingToken.setDeviceType(DeviceType.IOS);

            when(pushTokenRepository.findByUser(testUser)).thenReturn(Optional.of(existingToken));
            when(pushTokenRepository.save(any(PushToken.class))).thenReturn(testPushToken);

            // When
            PushToken registeredToken = pushTokenService.registerPushToken(
                testUser, "new-push-token", DeviceType.ANDROID, "device-123"
            );

            // Then
            assertThat(registeredToken).isNotNull();
            verify(pushTokenRepository).findByUser(testUser);
            verify(pushTokenRepository).delete(existingToken);
            verify(pushTokenRepository).save(any(PushToken.class));
        }

        @Test
        @DisplayName("Should register push token for iOS device")
        void shouldRegisterPushTokenForIosDevice() {
            // Given
            when(pushTokenRepository.findByUser(testUser)).thenReturn(Optional.empty());
            when(pushTokenRepository.save(any(PushToken.class))).thenReturn(testPushToken);

            // When
            pushTokenService.registerPushToken(testUser, "ios-push-token", DeviceType.IOS, "ios-device-456");

            // Then
            verify(pushTokenRepository).save(argThat(token -> 
                token.getToken().equals("ios-push-token") &&
                token.getDeviceType() == DeviceType.IOS &&
                token.getDeviceId().equals("ios-device-456")
            ));
        }

        @Test
        @DisplayName("Should register push token without device ID")
        void shouldRegisterPushTokenWithoutDeviceId() {
            // Given
            when(pushTokenRepository.findByUser(testUser)).thenReturn(Optional.empty());
            when(pushTokenRepository.save(any(PushToken.class))).thenReturn(testPushToken);

            // When
            pushTokenService.registerPushToken(testUser, "push-token", DeviceType.ANDROID, null);

            // Then
            verify(pushTokenRepository).save(argThat(token -> 
                token.getToken().equals("push-token") &&
                token.getDeviceId() == null
            ));
        }
    }

    @Nested
    @DisplayName("Token Update Tests")
    class TokenUpdateTests {

        @Test
        @DisplayName("Should update existing push token successfully")
        void shouldUpdateExistingPushTokenSuccessfully() {
            // Given
            when(pushTokenRepository.findByUser(testUser)).thenReturn(Optional.of(testPushToken));
            when(pushTokenRepository.save(any(PushToken.class))).thenReturn(testPushToken);

            // When
            PushToken updatedToken = pushTokenService.updatePushToken(
                testUser, "updated-push-token", DeviceType.IOS, "updated-device-456"
            );

            // Then
            assertThat(updatedToken).isNotNull();
            verify(pushTokenRepository).findByUser(testUser);
            verify(pushTokenRepository).save(argThat(token -> 
                token.getToken().equals("updated-push-token") &&
                token.getDeviceType() == DeviceType.IOS &&
                token.getDeviceId().equals("updated-device-456") &&
                token.getLastUsed() != null
            ));
        }

        @Test
        @DisplayName("Should throw exception when updating non-existent push token")
        void shouldThrowExceptionWhenUpdatingNonExistentPushToken() {
            // Given
            when(pushTokenRepository.findByUser(testUser)).thenReturn(Optional.empty());

            // When & Then
            assertThatThrownBy(() -> pushTokenService.updatePushToken(
                testUser, "updated-token", DeviceType.ANDROID, "device-123"
            ))
                    .isInstanceOf(IllegalArgumentException.class)
                    .hasMessageContaining("User does not have an existing push token");

            verify(pushTokenRepository, never()).save(any(PushToken.class));
        }
    }

    @Nested
    @DisplayName("Token Retrieval Tests")
    class TokenRetrievalTests {

        @Test
        @DisplayName("Should get push token by user")
        void shouldGetPushTokenByUser() {
            // Given
            when(pushTokenRepository.findByUser(testUser)).thenReturn(Optional.of(testPushToken));

            // When
            Optional<PushToken> foundToken = pushTokenService.getPushTokenByUser(testUser);

            // Then
            assertThat(foundToken).isPresent().contains(testPushToken);
            verify(pushTokenRepository).findByUser(testUser);
        }

        @Test
        @DisplayName("Should return empty when user has no push token")
        void shouldReturnEmptyWhenUserHasNoPushToken() {
            // Given
            when(pushTokenRepository.findByUser(testUser)).thenReturn(Optional.empty());

            // When
            Optional<PushToken> foundToken = pushTokenService.getPushTokenByUser(testUser);

            // Then
            assertThat(foundToken).isEmpty();
        }

        @Test
        @DisplayName("Should get push token by value")
        void shouldGetPushTokenByValue() {
            // Given
            when(pushTokenRepository.findByToken("test-push-token-value"))
                    .thenReturn(Optional.of(testPushToken));

            // When
            Optional<PushToken> foundToken = pushTokenService.getPushTokenByValue("test-push-token-value");

            // Then
            assertThat(foundToken).isPresent().contains(testPushToken);
            verify(pushTokenRepository).findByToken("test-push-token-value");
        }

        @Test
        @DisplayName("Should check if user has push token")
        void shouldCheckIfUserHasPushToken() {
            // Given
            when(pushTokenRepository.existsByUser(testUser)).thenReturn(true);

            User userWithoutToken = new User();
            userWithoutToken.setId(2L);
            when(pushTokenRepository.existsByUser(userWithoutToken)).thenReturn(false);

            // When & Then
            assertThat(pushTokenService.hasPushToken(testUser)).isTrue();
            assertThat(pushTokenService.hasPushToken(userWithoutToken)).isFalse();
        }

        @Test
        @DisplayName("Should get push tokens by device type")
        void shouldGetPushTokensByDeviceType() {
            // Given
            List<PushToken> androidTokens = Arrays.asList(testPushToken);
            when(pushTokenRepository.findByDeviceType(DeviceType.ANDROID)).thenReturn(androidTokens);

            // When
            List<PushToken> result = pushTokenService.getPushTokensByDeviceType(DeviceType.ANDROID);

            // Then
            assertThat(result).hasSize(1).contains(testPushToken);
            verify(pushTokenRepository).findByDeviceType(DeviceType.ANDROID);
        }

        @Test
        @DisplayName("Should get push tokens by device ID")
        void shouldGetPushTokensByDeviceId() {
            // Given
            List<PushToken> deviceTokens = Arrays.asList(testPushToken);
            when(pushTokenRepository.findByDeviceId("device-123")).thenReturn(deviceTokens);

            // When
            List<PushToken> result = pushTokenService.getPushTokensByDeviceId("device-123");

            // Then
            assertThat(result).hasSize(1).contains(testPushToken);
            verify(pushTokenRepository).findByDeviceId("device-123");
        }

        @Test
        @DisplayName("Should get count by device type")
        void shouldGetCountByDeviceType() {
            // Given
            when(pushTokenRepository.countByDeviceType(DeviceType.ANDROID)).thenReturn(5L);
            when(pushTokenRepository.countByDeviceType(DeviceType.IOS)).thenReturn(3L);

            // When & Then
            assertThat(pushTokenService.getCountByDeviceType(DeviceType.ANDROID)).isEqualTo(5L);
            assertThat(pushTokenService.getCountByDeviceType(DeviceType.IOS)).isEqualTo(3L);
        }
    }

    @Nested
    @DisplayName("Token Removal Tests")
    class TokenRemovalTests {

        @Test
        @DisplayName("Should remove push token for user successfully")
        void shouldRemovePushTokenForUserSuccessfully() {
            // Given
            when(pushTokenRepository.existsByUser(testUser)).thenReturn(true);

            // When
            boolean removed = pushTokenService.removePushToken(testUser);

            // Then
            assertThat(removed).isTrue();
            verify(pushTokenRepository).existsByUser(testUser);
            verify(pushTokenRepository).deleteByUser(testUser);
        }

        @Test
        @DisplayName("Should return false when removing non-existent push token")
        void shouldReturnFalseWhenRemovingNonExistentPushToken() {
            // Given
            when(pushTokenRepository.existsByUser(testUser)).thenReturn(false);

            // When
            boolean removed = pushTokenService.removePushToken(testUser);

            // Then
            assertThat(removed).isFalse();
            verify(pushTokenRepository).existsByUser(testUser);
            verify(pushTokenRepository, never()).deleteByUser(any(User.class));
        }

        @Test
        @DisplayName("Should remove push token by value successfully")
        void shouldRemovePushTokenByValueSuccessfully() {
            // Given
            when(pushTokenRepository.existsByToken("test-push-token-value")).thenReturn(true);

            // When
            boolean removed = pushTokenService.removePushTokenByValue("test-push-token-value");

            // Then
            assertThat(removed).isTrue();
            verify(pushTokenRepository).existsByToken("test-push-token-value");
            verify(pushTokenRepository).deleteByToken("test-push-token-value");
        }

        @Test
        @DisplayName("Should return false when removing non-existent token by value")
        void shouldReturnFalseWhenRemovingNonExistentTokenByValue() {
            // Given
            when(pushTokenRepository.existsByToken("nonexistent-token")).thenReturn(false);

            // When
            boolean removed = pushTokenService.removePushTokenByValue("nonexistent-token");

            // Then
            assertThat(removed).isFalse();
            verify(pushTokenRepository, never()).deleteByToken(anyString());
        }
    }

    @Nested
    @DisplayName("Last Used Update Tests")
    class LastUsedUpdateTests {

        @Test
        @DisplayName("Should update last used timestamp for user")
        void shouldUpdateLastUsedTimestampForUser() {
            // Given
            when(pushTokenRepository.updateLastUsedByUser(eq(testUser), any(LocalDateTime.class)))
                    .thenReturn(1);

            // When
            pushTokenService.updateLastUsed(testUser);

            // Then
            verify(pushTokenRepository).updateLastUsedByUser(eq(testUser), any(LocalDateTime.class));
        }

        @Test
        @DisplayName("Should handle update when user has no push token")
        void shouldHandleUpdateWhenUserHasNoPushToken() {
            // Given
            when(pushTokenRepository.updateLastUsedByUser(eq(testUser), any(LocalDateTime.class)))
                    .thenReturn(0);

            // When
            pushTokenService.updateLastUsed(testUser);

            // Then
            verify(pushTokenRepository).updateLastUsedByUser(eq(testUser), any(LocalDateTime.class));
        }

        @Test
        @DisplayName("Should update last used timestamp by token value")
        void shouldUpdateLastUsedTimestampByTokenValue() {
            // Given
            when(pushTokenRepository.updateLastUsedByToken(eq("test-push-token-value"), any(LocalDateTime.class)))
                    .thenReturn(1);

            // When
            pushTokenService.updateLastUsedByToken("test-push-token-value");

            // Then
            verify(pushTokenRepository).updateLastUsedByToken(eq("test-push-token-value"), any(LocalDateTime.class));
        }
    }

    @Nested
    @DisplayName("Token Query and Statistics Tests")
    class TokenQueryAndStatisticsTests {

        @Test
        @DisplayName("Should get tokens created after specific date")
        void shouldGetTokensCreatedAfterSpecificDate() {
            // Given
            LocalDateTime cutoffDate = LocalDateTime.now().minusDays(7);
            List<PushToken> recentTokens = Arrays.asList(testPushToken);
            when(pushTokenRepository.findByCreatedAtAfter(cutoffDate)).thenReturn(recentTokens);

            // When
            List<PushToken> result = pushTokenService.getTokensCreatedAfter(cutoffDate);

            // Then
            assertThat(result).hasSize(1).contains(testPushToken);
            verify(pushTokenRepository).findByCreatedAtAfter(cutoffDate);
        }

        @Test
        @DisplayName("Should get stale tokens")
        void shouldGetStaleTokens() {
            // Given
            List<PushToken> staleTokens = Arrays.asList(testPushToken);
            when(pushTokenRepository.findStaleTokens(any(LocalDateTime.class))).thenReturn(staleTokens);

            // When
            List<PushToken> result = pushTokenService.getStaleTokens();

            // Then
            assertThat(result).hasSize(1).contains(testPushToken);
            verify(pushTokenRepository).findStaleTokens(any(LocalDateTime.class));
        }

        @Test
        @DisplayName("Should get total token count")
        void shouldGetTotalTokenCount() {
            // Given
            when(pushTokenRepository.count()).thenReturn(10L);

            // When
            long count = pushTokenService.getTotalTokenCount();

            // Then
            assertThat(count).isEqualTo(10L);
            verify(pushTokenRepository).count();
        }

        @Test
        @DisplayName("Should check if token exists")
        void shouldCheckIfTokenExists() {
            // Given
            when(pushTokenRepository.existsByToken("existing-token")).thenReturn(true);
            when(pushTokenRepository.existsByToken("nonexistent-token")).thenReturn(false);

            // When & Then
            assertThat(pushTokenService.tokenExists("existing-token")).isTrue();
            assertThat(pushTokenService.tokenExists("nonexistent-token")).isFalse();
        }
    }

    @Nested
    @DisplayName("Token Cleanup Tests")
    class TokenCleanupTests {

        @Test
        @DisplayName("Should cleanup stale tokens")
        void shouldCleanupStaleTokens() {
            // Given
            when(pushTokenRepository.deleteStaleTokens(any(LocalDateTime.class))).thenReturn(3);

            // When
            pushTokenService.cleanupStaleTokens();

            // Then
            verify(pushTokenRepository).deleteStaleTokens(any(LocalDateTime.class));
        }

        @Test
        @DisplayName("Should handle cleanup with no stale tokens")
        void shouldHandleCleanupWithNoStaleTokens() {
            // Given
            when(pushTokenRepository.deleteStaleTokens(any(LocalDateTime.class))).thenReturn(0);

            // When
            pushTokenService.cleanupStaleTokens();

            // Then
            verify(pushTokenRepository).deleteStaleTokens(any(LocalDateTime.class));
        }
    }

    @Nested
    @DisplayName("Device Type Validation Tests")
    class DeviceTypeValidationTests {

        @Test
        @DisplayName("Should validate mobile device types successfully")
        void shouldValidateMobileDeviceTypesSuccessfully() {
            // When & Then
            assertThatCode(() -> pushTokenService.validateMobileDeviceType(DeviceType.ANDROID))
                    .doesNotThrowAnyException();
            assertThatCode(() -> pushTokenService.validateMobileDeviceType(DeviceType.IOS))
                    .doesNotThrowAnyException();
        }

        @Test
        @DisplayName("Should throw exception for web device type")
        void shouldThrowExceptionForWebDeviceType() {
            // When & Then
            assertThatThrownBy(() -> pushTokenService.validateMobileDeviceType(DeviceType.WEB))
                    .isInstanceOf(IllegalArgumentException.class)
                    .hasMessageContaining("Push tokens are only supported for mobile devices");
        }
    }

    @Nested
    @DisplayName("Single Token Per User Policy Tests")
    class SingleTokenPerUserPolicyTests {

        @Test
        @DisplayName("Should enforce single token per user policy")
        void shouldEnforceSingleTokenPerUserPolicy() {
            // Given
            PushToken existingToken = new PushToken();
            existingToken.setToken("existing-token");
            existingToken.setUser(testUser);
            existingToken.setDeviceType(DeviceType.IOS);

            when(pushTokenRepository.findByUser(testUser)).thenReturn(Optional.of(existingToken));
            when(pushTokenRepository.save(any(PushToken.class))).thenReturn(testPushToken);

            // When
            pushTokenService.registerPushToken(testUser, "new-token", DeviceType.ANDROID, "device-456");

            // Then
            verify(pushTokenRepository).delete(existingToken);
            verify(pushTokenRepository).save(argThat(token -> 
                token.getToken().equals("new-token") &&
                token.getDeviceType() == DeviceType.ANDROID
            ));
        }

        @Test
        @DisplayName("Should allow different users to have their own push tokens")
        void shouldAllowDifferentUsersToHaveTheirOwnPushTokens() {
            // Given
            User anotherUser = new User();
            anotherUser.setId(2L);
            anotherUser.setEmail("<EMAIL>");

            when(pushTokenRepository.findByUser(testUser)).thenReturn(Optional.empty());
            when(pushTokenRepository.findByUser(anotherUser)).thenReturn(Optional.empty());
            when(pushTokenRepository.save(any(PushToken.class))).thenReturn(testPushToken);

            // When
            pushTokenService.registerPushToken(testUser, "token1", DeviceType.ANDROID, "device1");
            pushTokenService.registerPushToken(anotherUser, "token2", DeviceType.IOS, "device2");

            // Then
            verify(pushTokenRepository, times(2)).save(any(PushToken.class));
            verify(pushTokenRepository, never()).delete(any(PushToken.class));
        }
    }

    @Nested
    @DisplayName("Edge Cases and Error Handling")
    class EdgeCasesAndErrorHandlingTests {

        @Test
        @DisplayName("Should handle null token value gracefully")
        void shouldHandleNullTokenValueGracefully() {
            // Given
            when(pushTokenRepository.findByUser(testUser)).thenReturn(Optional.empty());
            when(pushTokenRepository.save(any(PushToken.class))).thenReturn(testPushToken);

            // When
            pushTokenService.registerPushToken(testUser, null, DeviceType.ANDROID, "device-123");

            // Then
            verify(pushTokenRepository).save(argThat(token -> token.getToken() == null));
        }

        @Test
        @DisplayName("Should handle empty token value gracefully")
        void shouldHandleEmptyTokenValueGracefully() {
            // Given
            when(pushTokenRepository.findByUser(testUser)).thenReturn(Optional.empty());
            when(pushTokenRepository.save(any(PushToken.class))).thenReturn(testPushToken);

            // When
            pushTokenService.registerPushToken(testUser, "", DeviceType.ANDROID, "device-123");

            // Then
            verify(pushTokenRepository).save(argThat(token -> token.getToken().equals("")));
        }

        @Test
        @DisplayName("Should handle repository exceptions during registration")
        void shouldHandleRepositoryExceptionsDuringRegistration() {
            // Given
            when(pushTokenRepository.findByUser(testUser))
                    .thenThrow(new RuntimeException("Database error"));

            // When & Then
            assertThatThrownBy(() -> pushTokenService.registerPushToken(
                testUser, "token", DeviceType.ANDROID, "device-123"
            ))
                    .isInstanceOf(RuntimeException.class)
                    .hasMessageContaining("Database error");
        }

        @Test
        @DisplayName("Should handle user with null ID")
        void shouldHandleUserWithNullId() {
            // Given
            User userWithNullId = new User();
            userWithNullId.setId(null);
            userWithNullId.setEmail("<EMAIL>");

            when(pushTokenRepository.findByUser(userWithNullId)).thenReturn(Optional.empty());
            when(pushTokenRepository.save(any(PushToken.class))).thenReturn(testPushToken);

            // When
            PushToken result = pushTokenService.registerPushToken(
                userWithNullId, "token", DeviceType.ANDROID, "device-123"
            );

            // Then
            assertThat(result).isNotNull();
            verify(pushTokenRepository).save(any(PushToken.class));
        }
    }
}
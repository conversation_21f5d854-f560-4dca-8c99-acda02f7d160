package co.com.gedsys.authentication.config;

import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.test.context.TestPropertySource;

import javax.sql.DataSource;

/**
 * Test configuration for Docker Compose database integration.
 * This configuration uses Spring Docker Compose plugin to automatically
 * start and configure PostgreSQL container for integration tests.
 */
@TestConfiguration
@TestPropertySource(properties = {
    "spring.docker.compose.file=compose-test.yaml",
    "spring.docker.compose.lifecycle-management=start_and_stop",
    "spring.docker.compose.start.command=up",
    "spring.docker.compose.stop.command=down",
    "spring.docker.compose.stop.timeout=1m"
})
public class TestDatabaseConfig {
    
    /**
     * Creates a test DataSource that connects to the PostgreSQL container
     * managed by Spring Docker Compose plugin.
     * 
     * @return DataSource configured for test database
     */
    @Bean
    @Primary
    public DataSource testDataSource() {
        // Spring Docker Compose plugin will automatically configure the DataSource
        // based on the running PostgreSQL container
        return DataSourceBuilder.create()
                .url("*********************************************")
                .username("test_user")
                .password("test_password")
                .driverClassName("org.postgresql.Driver")
                .build();
    }
}
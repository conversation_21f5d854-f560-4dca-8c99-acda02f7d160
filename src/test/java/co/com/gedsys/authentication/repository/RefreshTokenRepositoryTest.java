package co.com.gedsys.authentication.repository;

import co.com.gedsys.authentication.entity.RefreshToken;
import co.com.gedsys.authentication.entity.Role;
import co.com.gedsys.authentication.entity.SessionType;
import co.com.gedsys.authentication.entity.User;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.*;

/**
 * Comprehensive @DataJpaTest for RefreshTokenRepository.
 * Tests custom queries, constraints, and session management functionality.
 */
@DataJpaTest
@DisplayName("Refresh Token Repository Tests")
class RefreshTokenRepositoryTest {

    @Autowired
    private TestEntityManager entityManager;

    @Autowired
    private RefreshTokenRepository refreshTokenRepository;

    private User testUser;
    private User anotherUser;
    private RefreshToken webToken;
    private RefreshToken mobileToken;
    private RefreshToken expiredToken;
    private RefreshToken usedToken;

    @BeforeEach
    void setUp() {
        // Create test users
        testUser = new User();
        testUser.setEmail("<EMAIL>");
        testUser.setPassword("password123");
        testUser.setRole(Role.USER);
        testUser.setEnabled(true);

        anotherUser = new User();
        anotherUser.setEmail("<EMAIL>");
        anotherUser.setPassword("password456");
        anotherUser.setRole(Role.USER);
        anotherUser.setEnabled(true);

        // Persist users
        testUser = entityManager.persistAndFlush(testUser);
        anotherUser = entityManager.persistAndFlush(anotherUser);

        // Create test refresh tokens
        webToken = new RefreshToken();
        webToken.setToken("web-token-123");
        webToken.setUser(testUser);
        webToken.setSessionType(SessionType.WEB);
        webToken.setExpiryDate(LocalDateTime.now().plusDays(7));
        webToken.setUsed(false);

        mobileToken = new RefreshToken();
        mobileToken.setToken("mobile-token-456");
        mobileToken.setUser(testUser);
        mobileToken.setSessionType(SessionType.MOBILE);
        mobileToken.setExpiryDate(LocalDateTime.now().plusDays(7));
        mobileToken.setUsed(false);

        expiredToken = new RefreshToken();
        expiredToken.setToken("expired-token-789");
        expiredToken.setUser(testUser);
        expiredToken.setSessionType(SessionType.WEB);
        expiredToken.setExpiryDate(LocalDateTime.now().minusDays(1)); // Expired
        expiredToken.setUsed(false);

        usedToken = new RefreshToken();
        usedToken.setToken("used-token-101");
        usedToken.setUser(anotherUser);
        usedToken.setSessionType(SessionType.WEB);
        usedToken.setExpiryDate(LocalDateTime.now().plusDays(7));
        usedToken.setUsed(true); // Already used
    }

    @Nested
    @DisplayName("Basic CRUD Operations")
    class BasicCrudOperations {

        @Test
        @DisplayName("Should save and find refresh token by ID")
        void shouldSaveAndFindRefreshTokenById() {
            // Given
            RefreshToken savedToken = entityManager.persistAndFlush(webToken);
            entityManager.clear();

            // When
            Optional<RefreshToken> foundToken = refreshTokenRepository.findById(savedToken.getId());

            // Then
            assertThat(foundToken).isPresent();
            assertThat(foundToken.get().getToken()).isEqualTo("web-token-123");
            assertThat(foundToken.get().getUser().getId()).isEqualTo(testUser.getId());
            assertThat(foundToken.get().getSessionType()).isEqualTo(SessionType.WEB);
            assertThat(foundToken.get().isUsed()).isFalse();
            assertThat(foundToken.get().getCreatedAt()).isNotNull();
        }

        @Test
        @DisplayName("Should delete refresh token successfully")
        void shouldDeleteRefreshTokenSuccessfully() {
            // Given
            RefreshToken savedToken = entityManager.persistAndFlush(webToken);
            Long tokenId = savedToken.getId();
            entityManager.clear();

            // When
            refreshTokenRepository.deleteById(tokenId);

            // Then
            Optional<RefreshToken> deletedToken = refreshTokenRepository.findById(tokenId);
            assertThat(deletedToken).isEmpty();
        }
    }

    @Nested
    @DisplayName("Token-based Queries")
    class TokenBasedQueries {

        @BeforeEach
        void setUpTokens() {
            entityManager.persistAndFlush(webToken);
            entityManager.persistAndFlush(mobileToken);
            entityManager.persistAndFlush(expiredToken);
            entityManager.persistAndFlush(usedToken);
            entityManager.clear();
        }

        @Test
        @DisplayName("Should find refresh token by token string")
        void shouldFindRefreshTokenByTokenString() {
            // When
            Optional<RefreshToken> foundToken = refreshTokenRepository.findByToken("web-token-123");

            // Then
            assertThat(foundToken).isPresent();
            assertThat(foundToken.get().getUser().getId()).isEqualTo(testUser.getId());
            assertThat(foundToken.get().getSessionType()).isEqualTo(SessionType.WEB);
        }

        @Test
        @DisplayName("Should return empty when token not found")
        void shouldReturnEmptyWhenTokenNotFound() {
            // When
            Optional<RefreshToken> foundToken = refreshTokenRepository.findByToken("nonexistent-token");

            // Then
            assertThat(foundToken).isEmpty();
        }

        @Test
        @DisplayName("Should find valid token by token string")
        void shouldFindValidTokenByTokenString() {
            // Given
            LocalDateTime currentTime = LocalDateTime.now();

            // When
            Optional<RefreshToken> validToken = refreshTokenRepository.findValidTokenByToken("web-token-123", currentTime);
            Optional<RefreshToken> expiredTokenResult = refreshTokenRepository.findValidTokenByToken("expired-token-789", currentTime);
            Optional<RefreshToken> usedTokenResult = refreshTokenRepository.findValidTokenByToken("used-token-101", currentTime);

            // Then
            assertThat(validToken).isPresent();
            assertThat(expiredTokenResult).isEmpty(); // Expired
            assertThat(usedTokenResult).isEmpty(); // Used
        }
    }

    @Nested
    @DisplayName("User-based Queries")
    class UserBasedQueries {

        @BeforeEach
        void setUpTokens() {
            entityManager.persistAndFlush(webToken);
            entityManager.persistAndFlush(mobileToken);
            entityManager.persistAndFlush(expiredToken);
            entityManager.persistAndFlush(usedToken);
            entityManager.clear();
        }

        @Test
        @DisplayName("Should find refresh token by user and session type")
        void shouldFindRefreshTokenByUserAndSessionType() {
            // When
            Optional<RefreshToken> webTokenResult = refreshTokenRepository.findByUserAndSessionType(testUser, SessionType.WEB);
            Optional<RefreshToken> mobileTokenResult = refreshTokenRepository.findByUserAndSessionType(testUser, SessionType.MOBILE);

            // Then
            assertThat(webTokenResult).isPresent();
            assertThat(webTokenResult.get().getToken()).isEqualTo("web-token-123");
            assertThat(mobileTokenResult).isPresent();
            assertThat(mobileTokenResult.get().getToken()).isEqualTo("mobile-token-456");
        }

        @Test
        @DisplayName("Should find all tokens for a user")
        void shouldFindAllTokensForUser() {
            // When
            List<RefreshToken> userTokens = refreshTokenRepository.findByUser(testUser);
            List<RefreshToken> anotherUserTokens = refreshTokenRepository.findByUser(anotherUser);

            // Then
            assertThat(userTokens).hasSize(3); // web, mobile, expired
            assertThat(anotherUserTokens).hasSize(1); // used token
        }

        @Test
        @DisplayName("Should find tokens by user and session type")
        void shouldFindTokensByUserAndSessionType() {
            // When
            List<RefreshToken> webTokens = refreshTokenRepository.findAllByUserAndSessionType(testUser, SessionType.WEB);
            List<RefreshToken> mobileTokens = refreshTokenRepository.findAllByUserAndSessionType(testUser, SessionType.MOBILE);

            // Then
            assertThat(webTokens).hasSize(2); // web token and expired token
            assertThat(mobileTokens).hasSize(1); // mobile token
        }

        @Test
        @DisplayName("Should find valid tokens for user")
        void shouldFindValidTokensForUser() {
            // Given
            LocalDateTime currentTime = LocalDateTime.now();

            // When
            List<RefreshToken> validTokens = refreshTokenRepository.findValidTokensByUser(testUser, currentTime);

            // Then
            assertThat(validTokens).hasSize(2); // web and mobile tokens (expired and used tokens excluded)
            assertThat(validTokens).extracting(RefreshToken::getToken)
                    .containsExactlyInAnyOrder("web-token-123", "mobile-token-456");
        }
    }

    @Nested
    @DisplayName("Token State Queries")
    class TokenStateQueries {

        @BeforeEach
        void setUpTokens() {
            entityManager.persistAndFlush(webToken);
            entityManager.persistAndFlush(mobileToken);
            entityManager.persistAndFlush(expiredToken);
            entityManager.persistAndFlush(usedToken);
            entityManager.clear();
        }

        @Test
        @DisplayName("Should find expired tokens")
        void shouldFindExpiredTokens() {
            // Given
            LocalDateTime currentTime = LocalDateTime.now();

            // When
            List<RefreshToken> expiredTokens = refreshTokenRepository.findExpiredTokens(currentTime);

            // Then
            assertThat(expiredTokens).hasSize(1);
            assertThat(expiredTokens.get(0).getToken()).isEqualTo("expired-token-789");
        }

        @Test
        @DisplayName("Should find used tokens")
        void shouldFindUsedTokens() {
            // When
            List<RefreshToken> usedTokens = refreshTokenRepository.findByUsedTrue();

            // Then
            assertThat(usedTokens).hasSize(1);
            assertThat(usedTokens.get(0).getToken()).isEqualTo("used-token-101");
        }
    }

    @Nested
    @DisplayName("Count Operations")
    class CountOperations {

        @BeforeEach
        void setUpTokens() {
            entityManager.persistAndFlush(webToken);
            entityManager.persistAndFlush(mobileToken);
            entityManager.persistAndFlush(expiredToken);
            entityManager.persistAndFlush(usedToken);
            entityManager.clear();
        }

        @Test
        @DisplayName("Should count valid tokens for user")
        void shouldCountValidTokensForUser() {
            // Given
            LocalDateTime currentTime = LocalDateTime.now();

            // When
            long testUserValidCount = refreshTokenRepository.countValidTokensByUser(testUser, currentTime);
            long anotherUserValidCount = refreshTokenRepository.countValidTokensByUser(anotherUser, currentTime);

            // Then
            assertThat(testUserValidCount).isEqualTo(2); // web and mobile tokens
            assertThat(anotherUserValidCount).isEqualTo(0); // used token is not valid
        }

        @Test
        @DisplayName("Should check if user has valid token for session type")
        void shouldCheckIfUserHasValidTokenForSessionType() {
            // Given
            LocalDateTime currentTime = LocalDateTime.now();

            // When & Then
            assertThat(refreshTokenRepository.hasValidTokenForSessionType(testUser, SessionType.WEB, currentTime)).isTrue();
            assertThat(refreshTokenRepository.hasValidTokenForSessionType(testUser, SessionType.MOBILE, currentTime)).isTrue();
            assertThat(refreshTokenRepository.hasValidTokenForSessionType(anotherUser, SessionType.WEB, currentTime)).isFalse();
        }
    }

    @Nested
    @DisplayName("Bulk Update Operations")
    class BulkUpdateOperations {

        @BeforeEach
        void setUpTokens() {
            entityManager.persistAndFlush(webToken);
            entityManager.persistAndFlush(mobileToken);
            entityManager.persistAndFlush(expiredToken);
            entityManager.persistAndFlush(usedToken);
            entityManager.clear();
        }

        @Test
        @DisplayName("Should mark token as used by token string")
        void shouldMarkTokenAsUsedByTokenString() {
            // When
            int updatedCount = refreshTokenRepository.markTokenAsUsed("web-token-123");
            entityManager.clear();

            // Then
            assertThat(updatedCount).isEqualTo(1);
            Optional<RefreshToken> updatedToken = refreshTokenRepository.findByToken("web-token-123");
            assertThat(updatedToken).isPresent();
            assertThat(updatedToken.get().isUsed()).isTrue();
        }

        @Test
        @DisplayName("Should mark tokens as used by user and session type")
        void shouldMarkTokensAsUsedByUserAndSessionType() {
            // When
            int updatedCount = refreshTokenRepository.markTokensAsUsedByUserAndSessionType(testUser, SessionType.WEB);
            entityManager.clear();

            // Then
            assertThat(updatedCount).isEqualTo(2); // web token and expired token
            
            List<RefreshToken> webTokens = refreshTokenRepository.findAllByUserAndSessionType(testUser, SessionType.WEB);
            assertThat(webTokens).allMatch(RefreshToken::isUsed);
            
            // Mobile token should remain unchanged
            Optional<RefreshToken> mobileTokenResult = refreshTokenRepository.findByToken("mobile-token-456");
            assertThat(mobileTokenResult).isPresent();
            assertThat(mobileTokenResult.get().isUsed()).isFalse();
        }
    }

    @Nested
    @DisplayName("Bulk Delete Operations")
    class BulkDeleteOperations {

        @BeforeEach
        void setUpTokens() {
            entityManager.persistAndFlush(webToken);
            entityManager.persistAndFlush(mobileToken);
            entityManager.persistAndFlush(expiredToken);
            entityManager.persistAndFlush(usedToken);
            entityManager.clear();
        }

        @Test
        @DisplayName("Should delete expired tokens")
        void shouldDeleteExpiredTokens() {
            // Given
            LocalDateTime currentTime = LocalDateTime.now();

            // When
            int deletedCount = refreshTokenRepository.deleteExpiredTokens(currentTime);

            // Then
            assertThat(deletedCount).isEqualTo(1);
            assertThat(refreshTokenRepository.findByToken("expired-token-789")).isEmpty();
            assertThat(refreshTokenRepository.findByToken("web-token-123")).isPresent(); // Should remain
        }

        @Test
        @DisplayName("Should delete used tokens")
        void shouldDeleteUsedTokens() {
            // When
            int deletedCount = refreshTokenRepository.deleteUsedTokens();

            // Then
            assertThat(deletedCount).isEqualTo(1);
            assertThat(refreshTokenRepository.findByToken("used-token-101")).isEmpty();
            assertThat(refreshTokenRepository.findByToken("web-token-123")).isPresent(); // Should remain
        }

        @Test
        @DisplayName("Should delete tokens by user")
        void shouldDeleteTokensByUser() {
            // When
            refreshTokenRepository.deleteByUser(testUser);
            entityManager.flush();

            // Then
            List<RefreshToken> remainingTokens = refreshTokenRepository.findByUser(testUser);
            assertThat(remainingTokens).isEmpty();
            
            // Another user's tokens should remain
            List<RefreshToken> anotherUserTokens = refreshTokenRepository.findByUser(anotherUser);
            assertThat(anotherUserTokens).hasSize(1);
        }

        @Test
        @DisplayName("Should delete tokens by user and session type")
        void shouldDeleteTokensByUserAndSessionType() {
            // When
            refreshTokenRepository.deleteByUserAndSessionType(testUser, SessionType.WEB);
            entityManager.flush();

            // Then
            List<RefreshToken> webTokens = refreshTokenRepository.findAllByUserAndSessionType(testUser, SessionType.WEB);
            assertThat(webTokens).isEmpty();
            
            // Mobile token should remain
            List<RefreshToken> mobileTokens = refreshTokenRepository.findAllByUserAndSessionType(testUser, SessionType.MOBILE);
            assertThat(mobileTokens).hasSize(1);
        }
    }

    @Nested
    @DisplayName("Constraints and Validation")
    class ConstraintsAndValidation {

        @Test
        @DisplayName("Should enforce unique token constraint")
        void shouldEnforceUniqueTokenConstraint() {
            // Given
            entityManager.persistAndFlush(webToken);

            RefreshToken duplicateToken = new RefreshToken();
            duplicateToken.setToken("web-token-123"); // Same token
            duplicateToken.setUser(anotherUser);
            duplicateToken.setSessionType(SessionType.MOBILE);
            duplicateToken.setExpiryDate(LocalDateTime.now().plusDays(7));

            // When & Then
            assertThatThrownBy(() -> {
                entityManager.persistAndFlush(duplicateToken);
            }).hasMessageContaining("could not execute statement");
        }

        @Test
        @DisplayName("Should enforce unique user-session type constraint")
        void shouldEnforceUniqueUserSessionTypeConstraint() {
            // Given
            entityManager.persistAndFlush(webToken);

            RefreshToken duplicateUserSessionType = new RefreshToken();
            duplicateUserSessionType.setToken("another-web-token");
            duplicateUserSessionType.setUser(testUser);
            duplicateUserSessionType.setSessionType(SessionType.WEB); // Same user and session type
            duplicateUserSessionType.setExpiryDate(LocalDateTime.now().plusDays(7));

            // When & Then
            assertThatThrownBy(() -> {
                entityManager.persistAndFlush(duplicateUserSessionType);
            }).hasMessageContaining("could not execute statement");
        }

        @Test
        @DisplayName("Should allow same user with different session types")
        void shouldAllowSameUserWithDifferentSessionTypes() {
            // Given
            entityManager.persistAndFlush(webToken);

            RefreshToken mobileTokenForSameUser = new RefreshToken();
            mobileTokenForSameUser.setToken("mobile-token-for-same-user");
            mobileTokenForSameUser.setUser(testUser);
            mobileTokenForSameUser.setSessionType(SessionType.MOBILE); // Different session type
            mobileTokenForSameUser.setExpiryDate(LocalDateTime.now().plusDays(7));

            // When & Then
            assertThatCode(() -> {
                entityManager.persistAndFlush(mobileTokenForSameUser);
            }).doesNotThrowAnyException();
        }

        @Test
        @DisplayName("Should set default values correctly")
        void shouldSetDefaultValuesCorrectly() {
            // Given
            RefreshToken tokenWithDefaults = new RefreshToken();
            tokenWithDefaults.setToken("token-with-defaults");
            tokenWithDefaults.setUser(testUser);
            tokenWithDefaults.setSessionType(SessionType.WEB);
            tokenWithDefaults.setExpiryDate(LocalDateTime.now().plusDays(7));
            // Not setting 'used' explicitly

            // When
            RefreshToken savedToken = entityManager.persistAndFlush(tokenWithDefaults);

            // Then
            assertThat(savedToken.isUsed()).isFalse();
            assertThat(savedToken.getCreatedAt()).isNotNull();
        }
    }

    @Nested
    @DisplayName("Edge Cases and Error Handling")
    class EdgeCasesAndErrorHandling {

        @Test
        @DisplayName("Should handle queries with null parameters")
        void shouldHandleQueriesWithNullParameters() {
            // Given
            entityManager.persistAndFlush(webToken);
            LocalDateTime currentTime = LocalDateTime.now();

            // When & Then
            assertThat(refreshTokenRepository.findValidTokenByToken(null, currentTime)).isEmpty();
            assertThat(refreshTokenRepository.findByToken(null)).isEmpty();
        }

        @Test
        @DisplayName("Should handle empty token string")
        void shouldHandleEmptyTokenString() {
            // Given
            LocalDateTime currentTime = LocalDateTime.now();

            // When & Then
            assertThat(refreshTokenRepository.findValidTokenByToken("", currentTime)).isEmpty();
            assertThat(refreshTokenRepository.findByToken("")).isEmpty();
        }

        @Test
        @DisplayName("Should handle future expiry dates correctly")
        void shouldHandleFutureExpiryDatesCorrectly() {
            // Given
            RefreshToken futureToken = new RefreshToken();
            futureToken.setToken("future-token");
            futureToken.setUser(testUser);
            futureToken.setSessionType(SessionType.WEB);
            futureToken.setExpiryDate(LocalDateTime.now().plusYears(1)); // Far future
            futureToken.setUsed(false);

            entityManager.persistAndFlush(futureToken);
            LocalDateTime currentTime = LocalDateTime.now();

            // When
            Optional<RefreshToken> validToken = refreshTokenRepository.findValidTokenByToken("future-token", currentTime);

            // Then
            assertThat(validToken).isPresent();
        }

        @Test
        @DisplayName("Should handle bulk operations with no matching records")
        void shouldHandleBulkOperationsWithNoMatchingRecords() {
            // When
            int deletedExpired = refreshTokenRepository.deleteExpiredTokens(LocalDateTime.now().minusYears(1));
            int deletedUsed = refreshTokenRepository.deleteUsedTokens();
            int markedAsUsed = refreshTokenRepository.markTokenAsUsed("nonexistent-token");

            // Then
            assertThat(deletedExpired).isEqualTo(0);
            assertThat(deletedUsed).isEqualTo(0);
            assertThat(markedAsUsed).isEqualTo(0);
        }
    }
}
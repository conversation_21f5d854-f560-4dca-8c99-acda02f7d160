package co.com.gedsys.authentication.repository;

import co.com.gedsys.authentication.entity.Role;
import co.com.gedsys.authentication.entity.User;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.assertj.core.api.Assertions.*;

/**
 * Comprehensive @DataJpaTest for UserRepository.
 * Tests custom queries, constraints, and pagination functionality.
 */
@DataJpaTest
@DisplayName("User Repository Tests")
class UserRepositoryTest {

    @Autowired
    private TestEntityManager entityManager;

    @Autowired
    private UserRepository userRepository;

    private User testUser;
    private User adminUser;
    private User disabledUser;

    @BeforeEach
    void setUp() {
        testUser = new User();
        testUser.setEmail("<EMAIL>");
        testUser.setPassword("password123");
        testUser.setFirstName("Test");
        testUser.setLastName("User");
        testUser.setRole(Role.USER);
        testUser.setEnabled(true);

        adminUser = new User();
        adminUser.setEmail("<EMAIL>");
        adminUser.setPassword("adminpass123");
        adminUser.setFirstName("Admin");
        adminUser.setLastName("User");
        adminUser.setRole(Role.ADMIN);
        adminUser.setEnabled(true);

        disabledUser = new User();
        disabledUser.setEmail("<EMAIL>");
        disabledUser.setPassword("disabledpass123");
        disabledUser.setFirstName("Disabled");
        disabledUser.setLastName("User");
        disabledUser.setRole(Role.USER);
        disabledUser.setEnabled(false);
    }

    @Nested
    @DisplayName("Basic CRUD Operations")
    class BasicCrudOperations {

        @Test
        @DisplayName("Should save and find user by ID")
        void shouldSaveAndFindUserById() {
            // Given
            User savedUser = entityManager.persistAndFlush(testUser);
            entityManager.clear();

            // When
            Optional<User> foundUser = userRepository.findById(savedUser.getId());

            // Then
            assertThat(foundUser).isPresent();
            assertThat(foundUser.get().getEmail()).isEqualTo("<EMAIL>");
            assertThat(foundUser.get().getFirstName()).isEqualTo("Test");
            assertThat(foundUser.get().getRole()).isEqualTo(Role.USER);
            assertThat(foundUser.get().isEnabled()).isTrue();
            assertThat(foundUser.get().getCreatedAt()).isNotNull();
            assertThat(foundUser.get().getUpdatedAt()).isNotNull();
        }

        @Test
        @DisplayName("Should update user successfully")
        void shouldUpdateUserSuccessfully() {
            // Given
            User savedUser = entityManager.persistAndFlush(testUser);
            entityManager.clear();

            // When
            savedUser.setFirstName("Updated");
            savedUser.setRole(Role.ADMIN);
            User updatedUser = userRepository.save(savedUser);

            // Then
            assertThat(updatedUser.getFirstName()).isEqualTo("Updated");
            assertThat(updatedUser.getRole()).isEqualTo(Role.ADMIN);
            assertThat(updatedUser.getUpdatedAt()).isAfter(updatedUser.getCreatedAt());
        }

        @Test
        @DisplayName("Should delete user successfully")
        void shouldDeleteUserSuccessfully() {
            // Given
            User savedUser = entityManager.persistAndFlush(testUser);
            Long userId = savedUser.getId();
            entityManager.clear();

            // When
            userRepository.deleteById(userId);

            // Then
            Optional<User> deletedUser = userRepository.findById(userId);
            assertThat(deletedUser).isEmpty();
        }
    }

    @Nested
    @DisplayName("Email-based Queries")
    class EmailBasedQueries {

        @Test
        @DisplayName("Should find user by email")
        void shouldFindUserByEmail() {
            // Given
            entityManager.persistAndFlush(testUser);
            entityManager.clear();

            // When
            Optional<User> foundUser = userRepository.findByEmail("<EMAIL>");

            // Then
            assertThat(foundUser).isPresent();
            assertThat(foundUser.get().getEmail()).isEqualTo("<EMAIL>");
        }

        @Test
        @DisplayName("Should return empty when user not found by email")
        void shouldReturnEmptyWhenUserNotFoundByEmail() {
            // When
            Optional<User> foundUser = userRepository.findByEmail("<EMAIL>");

            // Then
            assertThat(foundUser).isEmpty();
        }

        @Test
        @DisplayName("Should check if user exists by email")
        void shouldCheckIfUserExistsByEmail() {
            // Given
            entityManager.persistAndFlush(testUser);
            entityManager.clear();

            // When & Then
            assertThat(userRepository.existsByEmail("<EMAIL>")).isTrue();
            assertThat(userRepository.existsByEmail("<EMAIL>")).isFalse();
        }

        @Test
        @DisplayName("Should enforce unique email constraint")
        void shouldEnforceUniqueEmailConstraint() {
            // Given
            entityManager.persistAndFlush(testUser);

            User duplicateEmailUser = new User();
            duplicateEmailUser.setEmail("<EMAIL>"); // Same email
            duplicateEmailUser.setPassword("password456");
            duplicateEmailUser.setRole(Role.USER);

            // When & Then
            assertThatThrownBy(() -> {
                entityManager.persistAndFlush(duplicateEmailUser);
            }).hasMessageContaining("could not execute statement");
        }
    }

    @Nested
    @DisplayName("Custom Filter Queries")
    class CustomFilterQueries {

        @BeforeEach
        void setUpTestData() {
            entityManager.persistAndFlush(testUser);
            entityManager.persistAndFlush(adminUser);
            entityManager.persistAndFlush(disabledUser);
            entityManager.clear();
        }

        @Test
        @DisplayName("Should find users with email filter")
        void shouldFindUsersWithEmailFilter() {
            // Given
            Pageable pageable = PageRequest.of(0, 10);

            // When
            Page<User> result = userRepository.findUsersWithFilters("test", null, null, pageable);

            // Then
            assertThat(result.getContent()).hasSize(1);
            assertThat(result.getContent().get(0).getEmail()).isEqualTo("<EMAIL>");
        }

        @Test
        @DisplayName("Should find users with enabled filter")
        void shouldFindUsersWithEnabledFilter() {
            // Given
            Pageable pageable = PageRequest.of(0, 10);

            // When
            Page<User> enabledUsers = userRepository.findUsersWithFilters(null, true, null, pageable);
            Page<User> disabledUsers = userRepository.findUsersWithFilters(null, false, null, pageable);

            // Then
            assertThat(enabledUsers.getContent()).hasSize(2);
            assertThat(disabledUsers.getContent()).hasSize(1);
            assertThat(disabledUsers.getContent().get(0).getEmail()).isEqualTo("<EMAIL>");
        }

        @Test
        @DisplayName("Should find users with role filter")
        void shouldFindUsersWithRoleFilter() {
            // Given
            Pageable pageable = PageRequest.of(0, 10);

            // When
            Page<User> userRoleUsers = userRepository.findUsersWithFilters(null, null, Role.USER, pageable);
            Page<User> adminRoleUsers = userRepository.findUsersWithFilters(null, null, Role.ADMIN, pageable);

            // Then
            assertThat(userRoleUsers.getContent()).hasSize(2);
            assertThat(adminRoleUsers.getContent()).hasSize(1);
            assertThat(adminRoleUsers.getContent().get(0).getEmail()).isEqualTo("<EMAIL>");
        }

        @Test
        @DisplayName("Should find users with combined filters")
        void shouldFindUsersWithCombinedFilters() {
            // Given
            Pageable pageable = PageRequest.of(0, 10);

            // When
            Page<User> result = userRepository.findUsersWithFilters("admin", true, Role.ADMIN, pageable);

            // Then
            assertThat(result.getContent()).hasSize(1);
            assertThat(result.getContent().get(0).getEmail()).isEqualTo("<EMAIL>");
        }

        @Test
        @DisplayName("Should return all users when no filters applied")
        void shouldReturnAllUsersWhenNoFiltersApplied() {
            // Given
            Pageable pageable = PageRequest.of(0, 10);

            // When
            Page<User> result = userRepository.findUsersWithFilters(null, null, null, pageable);

            // Then
            assertThat(result.getContent()).hasSize(3);
        }

        @Test
        @DisplayName("Should handle case-insensitive email filter")
        void shouldHandleCaseInsensitiveEmailFilter() {
            // Given
            Pageable pageable = PageRequest.of(0, 10);

            // When
            Page<User> result = userRepository.findUsersWithFilters("TEST", null, null, pageable);

            // Then
            assertThat(result.getContent()).hasSize(1);
            assertThat(result.getContent().get(0).getEmail()).isEqualTo("<EMAIL>");
        }
    }

    @Nested
    @DisplayName("Pagination and Sorting")
    class PaginationAndSorting {

        @BeforeEach
        void setUpTestData() {
            // Create multiple users for pagination testing
            for (int i = 1; i <= 15; i++) {
                User user = new User();
                user.setEmail("user" + i + "@example.com");
                user.setPassword("password" + i);
                user.setFirstName("User" + i);
                user.setLastName("Test");
                user.setRole(i % 2 == 0 ? Role.ADMIN : Role.USER);
                user.setEnabled(i % 3 != 0); // Some disabled users
                entityManager.persist(user);
            }
            entityManager.flush();
            entityManager.clear();
        }

        @Test
        @DisplayName("Should paginate results correctly")
        void shouldPaginateResultsCorrectly() {
            // Given
            Pageable firstPage = PageRequest.of(0, 5);
            Pageable secondPage = PageRequest.of(1, 5);

            // When
            Page<User> page1 = userRepository.findUsersWithFilters(null, null, null, firstPage);
            Page<User> page2 = userRepository.findUsersWithFilters(null, null, null, secondPage);

            // Then
            assertThat(page1.getContent()).hasSize(5);
            assertThat(page2.getContent()).hasSize(5);
            assertThat(page1.getTotalElements()).isEqualTo(15);
            assertThat(page1.getTotalPages()).isEqualTo(3);
            assertThat(page1.isFirst()).isTrue();
            assertThat(page1.isLast()).isFalse();
            assertThat(page2.isFirst()).isFalse();
            assertThat(page2.isLast()).isFalse();
        }

        @Test
        @DisplayName("Should sort results correctly")
        void shouldSortResultsCorrectly() {
            // Given
            Pageable sortedByEmail = PageRequest.of(0, 10, Sort.by("email").ascending());
            Pageable sortedByRole = PageRequest.of(0, 10, Sort.by("role").descending());

            // When
            Page<User> emailSorted = userRepository.findUsersWithFilters(null, null, null, sortedByEmail);
            Page<User> roleSorted = userRepository.findUsersWithFilters(null, null, null, sortedByRole);

            // Then
            assertThat(emailSorted.getContent().get(0).getEmail()).startsWith("user1");
            assertThat(emailSorted.getContent().get(1).getEmail()).startsWith("user10");
            
            assertThat(roleSorted.getContent().get(0).getRole()).isEqualTo(Role.USER);
        }
    }

    @Nested
    @DisplayName("Specialized Query Methods")
    class SpecializedQueryMethods {

        @BeforeEach
        void setUpTestData() {
            entityManager.persistAndFlush(testUser);
            entityManager.persistAndFlush(adminUser);
            entityManager.persistAndFlush(disabledUser);
            entityManager.clear();
        }

        @Test
        @DisplayName("Should find enabled users only")
        void shouldFindEnabledUsersOnly() {
            // Given
            Pageable pageable = PageRequest.of(0, 10);

            // When
            Page<User> enabledUsers = userRepository.findByEnabledTrue(pageable);

            // Then
            assertThat(enabledUsers.getContent()).hasSize(2);
            assertThat(enabledUsers.getContent()).allMatch(User::isEnabled);
        }

        @Test
        @DisplayName("Should find users by role")
        void shouldFindUsersByRole() {
            // Given
            Pageable pageable = PageRequest.of(0, 10);

            // When
            Page<User> userRoleUsers = userRepository.findByRole(Role.USER, pageable);
            Page<User> adminRoleUsers = userRepository.findByRole(Role.ADMIN, pageable);

            // Then
            assertThat(userRoleUsers.getContent()).hasSize(2);
            assertThat(adminRoleUsers.getContent()).hasSize(1);
            assertThat(userRoleUsers.getContent()).allMatch(user -> user.getRole() == Role.USER);
            assertThat(adminRoleUsers.getContent()).allMatch(user -> user.getRole() == Role.ADMIN);
        }
    }

    @Nested
    @DisplayName("Count Operations")
    class CountOperations {

        @BeforeEach
        void setUpTestData() {
            entityManager.persistAndFlush(testUser);
            entityManager.persistAndFlush(adminUser);
            entityManager.persistAndFlush(disabledUser);
            entityManager.clear();
        }

        @Test
        @DisplayName("Should count users by enabled status")
        void shouldCountUsersByEnabledStatus() {
            // When
            long enabledCount = userRepository.countByEnabled(true);
            long disabledCount = userRepository.countByEnabled(false);

            // Then
            assertThat(enabledCount).isEqualTo(2);
            assertThat(disabledCount).isEqualTo(1);
        }

        @Test
        @DisplayName("Should count users by role")
        void shouldCountUsersByRole() {
            // When
            long userRoleCount = userRepository.countByRole(Role.USER);
            long adminRoleCount = userRepository.countByRole(Role.ADMIN);

            // Then
            assertThat(userRoleCount).isEqualTo(2);
            assertThat(adminRoleCount).isEqualTo(1);
        }
    }

    @Nested
    @DisplayName("Entity Constraints and Validation")
    class EntityConstraintsAndValidation {

        @Test
        @DisplayName("Should enforce not null constraints")
        void shouldEnforceNotNullConstraints() {
            // Given
            User invalidUser = new User();
            // Missing required fields

            // When & Then
            assertThatThrownBy(() -> {
                entityManager.persistAndFlush(invalidUser);
            }).hasMessageContaining("not-null");
        }

        @Test
        @DisplayName("Should set default values correctly")
        void shouldSetDefaultValuesCorrectly() {
            // Given
            User userWithDefaults = new User();
            userWithDefaults.setEmail("<EMAIL>");
            userWithDefaults.setPassword("password123");
            // Not setting role and enabled explicitly

            // When
            User savedUser = entityManager.persistAndFlush(userWithDefaults);

            // Then
            assertThat(savedUser.getRole()).isEqualTo(Role.USER);
            assertThat(savedUser.isEnabled()).isTrue();
            assertThat(savedUser.getCreatedAt()).isNotNull();
            assertThat(savedUser.getUpdatedAt()).isNotNull();
        }

        @Test
        @DisplayName("Should handle timestamp fields correctly")
        void shouldHandleTimestampFieldsCorrectly() {
            // Given
            LocalDateTime beforeSave = LocalDateTime.now();

            // When
            User savedUser = entityManager.persistAndFlush(testUser);

            // Then
            assertThat(savedUser.getCreatedAt()).isAfter(beforeSave.minusSeconds(1));
            assertThat(savedUser.getUpdatedAt()).isAfter(beforeSave.minusSeconds(1));
            assertThat(savedUser.getCreatedAt()).isEqualTo(savedUser.getUpdatedAt());
        }

        @Test
        @DisplayName("Should update timestamp on modification")
        void shouldUpdateTimestampOnModification() {
            // Given
            User savedUser = entityManager.persistAndFlush(testUser);
            LocalDateTime originalUpdatedAt = savedUser.getUpdatedAt();
            entityManager.clear();

            // Wait a bit to ensure timestamp difference
            try {
                Thread.sleep(10);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }

            // When
            savedUser.setFirstName("Modified");
            User updatedUser = entityManager.merge(savedUser);
            entityManager.flush();

            // Then
            assertThat(updatedUser.getUpdatedAt()).isAfter(originalUpdatedAt);
            assertThat(updatedUser.getCreatedAt()).isEqualTo(originalUpdatedAt);
        }
    }

    @Nested
    @DisplayName("Edge Cases and Error Handling")
    class EdgeCasesAndErrorHandling {

        @Test
        @DisplayName("Should handle empty filter parameters")
        void shouldHandleEmptyFilterParameters() {
            // Given
            entityManager.persistAndFlush(testUser);
            Pageable pageable = PageRequest.of(0, 10);

            // When
            Page<User> result = userRepository.findUsersWithFilters("", null, null, pageable);

            // Then
            assertThat(result.getContent()).hasSize(1); // Empty string should match all
        }

        @Test
        @DisplayName("Should handle special characters in email filter")
        void shouldHandleSpecialCharactersInEmailFilter() {
            // Given
            User specialUser = new User();
            specialUser.setEmail("<EMAIL>");
            specialUser.setPassword("password123");
            specialUser.setRole(Role.USER);
            entityManager.persistAndFlush(specialUser);

            Pageable pageable = PageRequest.of(0, 10);

            // When
            Page<User> result = userRepository.findUsersWithFilters("+test", null, null, pageable);

            // Then
            assertThat(result.getContent()).hasSize(1);
            assertThat(result.getContent().get(0).getEmail()).isEqualTo("<EMAIL>");
        }

        @Test
        @DisplayName("Should handle large page sizes")
        void shouldHandleLargePageSizes() {
            // Given
            entityManager.persistAndFlush(testUser);
            Pageable largePage = PageRequest.of(0, 1000);

            // When
            Page<User> result = userRepository.findUsersWithFilters(null, null, null, largePage);

            // Then
            assertThat(result.getContent()).hasSize(1);
            assertThat(result.getTotalElements()).isEqualTo(1);
        }

        @Test
        @DisplayName("Should handle page beyond available data")
        void shouldHandlePageBeyondAvailableData() {
            // Given
            entityManager.persistAndFlush(testUser);
            Pageable beyondDataPage = PageRequest.of(10, 10);

            // When
            Page<User> result = userRepository.findUsersWithFilters(null, null, null, beyondDataPage);

            // Then
            assertThat(result.getContent()).isEmpty();
            assertThat(result.getTotalElements()).isEqualTo(1);
            assertThat(result.getTotalPages()).isEqualTo(1);
        }
    }
}
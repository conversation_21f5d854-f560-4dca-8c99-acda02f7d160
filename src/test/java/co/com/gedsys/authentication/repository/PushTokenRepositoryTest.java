package co.com.gedsys.authentication.repository;

import co.com.gedsys.authentication.entity.DeviceType;
import co.com.gedsys.authentication.entity.PushToken;
import co.com.gedsys.authentication.entity.Role;
import co.com.gedsys.authentication.entity.User;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.*;

/**
 * Comprehensive @DataJpaTest for PushTokenRepository.
 * Tests custom queries, constraints, and device management functionality.
 */
@DataJpaTest
@DisplayName("Push Token Repository Tests")
class PushTokenRepositoryTest {

    @Autowired
    private TestEntityManager entityManager;

    @Autowired
    private PushTokenRepository pushTokenRepository;

    private User testUser;
    private User anotherUser;
    private PushToken androidToken;
    private PushToken iosToken;
    private PushToken staleToken;

    @BeforeEach
    void setUp() {
        // Create test users
        testUser = new User();
        testUser.setEmail("<EMAIL>");
        testUser.setPassword("password123");
        testUser.setRole(Role.USER);
        testUser.setEnabled(true);

        anotherUser = new User();
        anotherUser.setEmail("<EMAIL>");
        anotherUser.setPassword("password456");
        anotherUser.setRole(Role.USER);
        anotherUser.setEnabled(true);

        // Persist users
        testUser = entityManager.persistAndFlush(testUser);
        anotherUser = entityManager.persistAndFlush(anotherUser);

        // Create test push tokens
        androidToken = new PushToken();
        androidToken.setToken("android-push-token-123");
        androidToken.setUser(testUser);
        androidToken.setDeviceType(DeviceType.ANDROID);
        androidToken.setDeviceId("android-device-456");
        androidToken.setLastUsed(LocalDateTime.now());

        iosToken = new PushToken();
        iosToken.setToken("ios-push-token-789");
        iosToken.setUser(anotherUser);
        iosToken.setDeviceType(DeviceType.IOS);
        iosToken.setDeviceId("ios-device-101");
        iosToken.setLastUsed(LocalDateTime.now());

        staleToken = new PushToken();
        staleToken.setToken("stale-push-token-999");
        staleToken.setUser(testUser);
        staleToken.setDeviceType(DeviceType.ANDROID);
        staleToken.setDeviceId("stale-device-888");
        staleToken.setLastUsed(LocalDateTime.now().minusDays(45)); // Stale token
    }

    @Nested
    @DisplayName("Basic CRUD Operations")
    class BasicCrudOperations {

        @Test
        @DisplayName("Should save and find push token by ID")
        void shouldSaveAndFindPushTokenById() {
            // Given
            PushToken savedToken = entityManager.persistAndFlush(androidToken);
            entityManager.clear();

            // When
            Optional<PushToken> foundToken = pushTokenRepository.findById(savedToken.getId());

            // Then
            assertThat(foundToken).isPresent();
            assertThat(foundToken.get().getToken()).isEqualTo("android-push-token-123");
            assertThat(foundToken.get().getUser().getId()).isEqualTo(testUser.getId());
            assertThat(foundToken.get().getDeviceType()).isEqualTo(DeviceType.ANDROID);
            assertThat(foundToken.get().getDeviceId()).isEqualTo("android-device-456");
            assertThat(foundToken.get().getCreatedAt()).isNotNull();
            assertThat(foundToken.get().getLastUsed()).isNotNull();
        }

        @Test
        @DisplayName("Should update push token successfully")
        void shouldUpdatePushTokenSuccessfully() {
            // Given
            PushToken savedToken = entityManager.persistAndFlush(androidToken);
            entityManager.clear();

            // When
            savedToken.setToken("updated-android-token");
            savedToken.setDeviceId("updated-device-id");
            PushToken updatedToken = pushTokenRepository.save(savedToken);

            // Then
            assertThat(updatedToken.getToken()).isEqualTo("updated-android-token");
            assertThat(updatedToken.getDeviceId()).isEqualTo("updated-device-id");
        }

        @Test
        @DisplayName("Should delete push token successfully")
        void shouldDeletePushTokenSuccessfully() {
            // Given
            PushToken savedToken = entityManager.persistAndFlush(androidToken);
            Long tokenId = savedToken.getId();
            entityManager.clear();

            // When
            pushTokenRepository.deleteById(tokenId);

            // Then
            Optional<PushToken> deletedToken = pushTokenRepository.findById(tokenId);
            assertThat(deletedToken).isEmpty();
        }
    }

    @Nested
    @DisplayName("User-based Queries")
    class UserBasedQueries {

        @BeforeEach
        void setUpTokens() {
            entityManager.persistAndFlush(androidToken);
            entityManager.persistAndFlush(iosToken);
            entityManager.clear();
        }

        @Test
        @DisplayName("Should find push token by user")
        void shouldFindPushTokenByUser() {
            // When
            Optional<PushToken> testUserToken = pushTokenRepository.findByUser(testUser);
            Optional<PushToken> anotherUserToken = pushTokenRepository.findByUser(anotherUser);

            // Then
            assertThat(testUserToken).isPresent();
            assertThat(testUserToken.get().getToken()).isEqualTo("android-push-token-123");
            assertThat(anotherUserToken).isPresent();
            assertThat(anotherUserToken.get().getToken()).isEqualTo("ios-push-token-789");
        }

        @Test
        @DisplayName("Should return empty when user has no push token")
        void shouldReturnEmptyWhenUserHasNoPushToken() {
            // Given
            User userWithoutToken = new User();
            userWithoutToken.setEmail("<EMAIL>");
            userWithoutToken.setPassword("password");
            userWithoutToken.setRole(Role.USER);
            userWithoutToken.setEnabled(true);
            userWithoutToken = entityManager.persistAndFlush(userWithoutToken);

            // When
            Optional<PushToken> result = pushTokenRepository.findByUser(userWithoutToken);

            // Then
            assertThat(result).isEmpty();
        }

        @Test
        @DisplayName("Should check if user has push token")
        void shouldCheckIfUserHasPushToken() {
            // When & Then
            assertThat(pushTokenRepository.existsByUser(testUser)).isTrue();
            assertThat(pushTokenRepository.existsByUser(anotherUser)).isTrue();
        }

        @Test
        @DisplayName("Should delete push token by user")
        void shouldDeletePushTokenByUser() {
            // When
            pushTokenRepository.deleteByUser(testUser);
            entityManager.flush();

            // Then
            assertThat(pushTokenRepository.findByUser(testUser)).isEmpty();
            assertThat(pushTokenRepository.findByUser(anotherUser)).isPresent(); // Should remain
        }
    }

    @Nested
    @DisplayName("Token-based Queries")
    class TokenBasedQueries {

        @BeforeEach
        void setUpTokens() {
            entityManager.persistAndFlush(androidToken);
            entityManager.persistAndFlush(iosToken);
            entityManager.clear();
        }

        @Test
        @DisplayName("Should find push token by token string")
        void shouldFindPushTokenByTokenString() {
            // When
            Optional<PushToken> foundToken = pushTokenRepository.findByToken("android-push-token-123");

            // Then
            assertThat(foundToken).isPresent();
            assertThat(foundToken.get().getUser().getId()).isEqualTo(testUser.getId());
            assertThat(foundToken.get().getDeviceType()).isEqualTo(DeviceType.ANDROID);
        }

        @Test
        @DisplayName("Should return empty when token not found")
        void shouldReturnEmptyWhenTokenNotFound() {
            // When
            Optional<PushToken> foundToken = pushTokenRepository.findByToken("nonexistent-token");

            // Then
            assertThat(foundToken).isEmpty();
        }

        @Test
        @DisplayName("Should check if token exists")
        void shouldCheckIfTokenExists() {
            // When & Then
            assertThat(pushTokenRepository.existsByToken("android-push-token-123")).isTrue();
            assertThat(pushTokenRepository.existsByToken("nonexistent-token")).isFalse();
        }

        @Test
        @DisplayName("Should delete push token by token string")
        void shouldDeletePushTokenByTokenString() {
            // When
            pushTokenRepository.deleteByToken("android-push-token-123");
            entityManager.flush();

            // Then
            assertThat(pushTokenRepository.findByToken("android-push-token-123")).isEmpty();
            assertThat(pushTokenRepository.findByToken("ios-push-token-789")).isPresent(); // Should remain
        }
    }

    @Nested
    @DisplayName("Device-based Queries")
    class DeviceBasedQueries {

        @BeforeEach
        void setUpTokens() {
            entityManager.persistAndFlush(androidToken);
            entityManager.persistAndFlush(iosToken);
            entityManager.persistAndFlush(staleToken);
            entityManager.clear();
        }

        @Test
        @DisplayName("Should find push tokens by device type")
        void shouldFindPushTokensByDeviceType() {
            // When
            List<PushToken> androidTokens = pushTokenRepository.findByDeviceType(DeviceType.ANDROID);
            List<PushToken> iosTokens = pushTokenRepository.findByDeviceType(DeviceType.IOS);

            // Then
            assertThat(androidTokens).hasSize(2); // androidToken and staleToken
            assertThat(iosTokens).hasSize(1);
            assertThat(androidTokens).extracting(PushToken::getDeviceType)
                    .containsOnly(DeviceType.ANDROID);
            assertThat(iosTokens).extracting(PushToken::getDeviceType)
                    .containsOnly(DeviceType.IOS);
        }

        @Test
        @DisplayName("Should find push tokens by device ID")
        void shouldFindPushTokensByDeviceId() {
            // When
            List<PushToken> androidDeviceTokens = pushTokenRepository.findByDeviceId("android-device-456");
            List<PushToken> iosDeviceTokens = pushTokenRepository.findByDeviceId("ios-device-101");

            // Then
            assertThat(androidDeviceTokens).hasSize(1);
            assertThat(androidDeviceTokens.get(0).getToken()).isEqualTo("android-push-token-123");
            assertThat(iosDeviceTokens).hasSize(1);
            assertThat(iosDeviceTokens.get(0).getToken()).isEqualTo("ios-push-token-789");
        }

        @Test
        @DisplayName("Should find push tokens by device type and device ID")
        void shouldFindPushTokensByDeviceTypeAndDeviceId() {
            // When
            List<PushToken> specificTokens = pushTokenRepository.findByDeviceTypeAndDeviceId(
                    DeviceType.ANDROID, "android-device-456");

            // Then
            assertThat(specificTokens).hasSize(1);
            assertThat(specificTokens.get(0).getToken()).isEqualTo("android-push-token-123");
        }

        @Test
        @DisplayName("Should count push tokens by device type")
        void shouldCountPushTokensByDeviceType() {
            // When
            long androidCount = pushTokenRepository.countByDeviceType(DeviceType.ANDROID);
            long iosCount = pushTokenRepository.countByDeviceType(DeviceType.IOS);

            // Then
            assertThat(androidCount).isEqualTo(2);
            assertThat(iosCount).isEqualTo(1);
        }
    }

    @Nested
    @DisplayName("Time-based Queries")
    class TimeBasedQueries {

        @BeforeEach
        void setUpTokens() {
            entityManager.persistAndFlush(androidToken);
            entityManager.persistAndFlush(iosToken);
            entityManager.persistAndFlush(staleToken);
            entityManager.clear();
        }

        @Test
        @DisplayName("Should find tokens created after specific date")
        void shouldFindTokensCreatedAfterSpecificDate() {
            // Given
            LocalDateTime cutoffDate = LocalDateTime.now().minusHours(1);

            // When
            List<PushToken> recentTokens = pushTokenRepository.findByCreatedAtAfter(cutoffDate);

            // Then
            assertThat(recentTokens).hasSize(3); // All tokens were created recently
        }

        @Test
        @DisplayName("Should find tokens created before specific date")
        void shouldFindTokensCreatedBeforeSpecificDate() {
            // Given
            LocalDateTime futureDate = LocalDateTime.now().plusDays(1);

            // When
            List<PushToken> pastTokens = pushTokenRepository.findByCreatedAtBefore(futureDate);

            // Then
            assertThat(pastTokens).hasSize(3); // All tokens were created in the past
        }

        @Test
        @DisplayName("Should find stale tokens")
        void shouldFindStaleTokens() {
            // Given
            LocalDateTime cutoffDate = LocalDateTime.now().minusDays(30);

            // When
            List<PushToken> staleTokens = pushTokenRepository.findStaleTokens(cutoffDate);

            // Then
            assertThat(staleTokens).hasSize(1);
            assertThat(staleTokens.get(0).getToken()).isEqualTo("stale-push-token-999");
        }

        @Test
        @DisplayName("Should delete stale tokens")
        void shouldDeleteStaleTokens() {
            // Given
            LocalDateTime cutoffDate = LocalDateTime.now().minusDays(30);

            // When
            int deletedCount = pushTokenRepository.deleteStaleTokens(cutoffDate);

            // Then
            assertThat(deletedCount).isEqualTo(1);
            assertThat(pushTokenRepository.findByToken("stale-push-token-999")).isEmpty();
            assertThat(pushTokenRepository.findByToken("android-push-token-123")).isPresent(); // Should remain
        }
    }

    @Nested
    @DisplayName("Update Operations")
    class UpdateOperations {

        @BeforeEach
        void setUpTokens() {
            entityManager.persistAndFlush(androidToken);
            entityManager.persistAndFlush(iosToken);
            entityManager.clear();
        }

        @Test
        @DisplayName("Should update last used timestamp by user")
        void shouldUpdateLastUsedTimestampByUser() {
            // Given
            LocalDateTime newLastUsed = LocalDateTime.now().plusHours(1);

            // When
            int updatedCount = pushTokenRepository.updateLastUsedByUser(testUser, newLastUsed);
            entityManager.clear();

            // Then
            assertThat(updatedCount).isEqualTo(1);
            Optional<PushToken> updatedToken = pushTokenRepository.findByUser(testUser);
            assertThat(updatedToken).isPresent();
            assertThat(updatedToken.get().getLastUsed()).isEqualToIgnoringNanos(newLastUsed);
        }

        @Test
        @DisplayName("Should update last used timestamp by token")
        void shouldUpdateLastUsedTimestampByToken() {
            // Given
            LocalDateTime newLastUsed = LocalDateTime.now().plusHours(2);

            // When
            int updatedCount = pushTokenRepository.updateLastUsedByToken("android-push-token-123", newLastUsed);
            entityManager.clear();

            // Then
            assertThat(updatedCount).isEqualTo(1);
            Optional<PushToken> updatedToken = pushTokenRepository.findByToken("android-push-token-123");
            assertThat(updatedToken).isPresent();
            assertThat(updatedToken.get().getLastUsed()).isEqualToIgnoringNanos(newLastUsed);
        }

        @Test
        @DisplayName("Should return zero when updating non-existent token")
        void shouldReturnZeroWhenUpdatingNonExistentToken() {
            // Given
            LocalDateTime newLastUsed = LocalDateTime.now();

            // When
            int updatedCount = pushTokenRepository.updateLastUsedByToken("nonexistent-token", newLastUsed);

            // Then
            assertThat(updatedCount).isEqualTo(0);
        }
    }

    @Nested
    @DisplayName("Constraints and Validation")
    class ConstraintsAndValidation {

        @Test
        @DisplayName("Should enforce unique user constraint")
        void shouldEnforceUniqueUserConstraint() {
            // Given
            entityManager.persistAndFlush(androidToken);

            PushToken duplicateUserToken = new PushToken();
            duplicateUserToken.setToken("another-token-for-same-user");
            duplicateUserToken.setUser(testUser); // Same user
            duplicateUserToken.setDeviceType(DeviceType.IOS);
            duplicateUserToken.setDeviceId("different-device");

            // When & Then
            assertThatThrownBy(() -> {
                entityManager.persistAndFlush(duplicateUserToken);
            }).hasMessageContaining("could not execute statement");
        }

        @Test
        @DisplayName("Should allow different users to have push tokens")
        void shouldAllowDifferentUsersToHavePushTokens() {
            // Given
            entityManager.persistAndFlush(androidToken);

            PushToken anotherUserToken = new PushToken();
            anotherUserToken.setToken("token-for-another-user");
            anotherUserToken.setUser(anotherUser); // Different user
            anotherUserToken.setDeviceType(DeviceType.ANDROID);
            anotherUserToken.setDeviceId("another-device");

            // When & Then
            assertThatCode(() -> {
                entityManager.persistAndFlush(anotherUserToken);
            }).doesNotThrowAnyException();
        }

        @Test
        @DisplayName("Should set default values correctly")
        void shouldSetDefaultValuesCorrectly() {
            // Given
            PushToken tokenWithDefaults = new PushToken();
            tokenWithDefaults.setToken("token-with-defaults");
            tokenWithDefaults.setUser(testUser);
            tokenWithDefaults.setDeviceType(DeviceType.ANDROID);
            // Not setting lastUsed explicitly

            // When
            PushToken savedToken = entityManager.persistAndFlush(tokenWithDefaults);

            // Then
            assertThat(savedToken.getCreatedAt()).isNotNull();
            // lastUsed can be null initially
        }

        @Test
        @DisplayName("Should handle null device ID")
        void shouldHandleNullDeviceId() {
            // Given
            PushToken tokenWithNullDeviceId = new PushToken();
            tokenWithNullDeviceId.setToken("token-with-null-device-id");
            tokenWithNullDeviceId.setUser(testUser);
            tokenWithNullDeviceId.setDeviceType(DeviceType.ANDROID);
            tokenWithNullDeviceId.setDeviceId(null);

            // When & Then
            assertThatCode(() -> {
                entityManager.persistAndFlush(tokenWithNullDeviceId);
            }).doesNotThrowAnyException();
        }
    }

    @Nested
    @DisplayName("Edge Cases and Error Handling")
    class EdgeCasesAndErrorHandling {

        @Test
        @DisplayName("Should handle queries with null parameters")
        void shouldHandleQueriesWithNullParameters() {
            // When & Then
            assertThat(pushTokenRepository.findByToken(null)).isEmpty();
            assertThat(pushTokenRepository.findByDeviceId(null)).isEmpty();
            assertThat(pushTokenRepository.existsByToken(null)).isFalse();
        }

        @Test
        @DisplayName("Should handle empty token string")
        void shouldHandleEmptyTokenString() {
            // When & Then
            assertThat(pushTokenRepository.findByToken("")).isEmpty();
            assertThat(pushTokenRepository.existsByToken("")).isFalse();
        }

        @Test
        @DisplayName("Should handle bulk operations with no matching records")
        void shouldHandleBulkOperationsWithNoMatchingRecords() {
            // Given
            LocalDateTime futureDate = LocalDateTime.now().plusDays(1);

            // When
            int deletedStale = pushTokenRepository.deleteStaleTokens(futureDate);
            int updatedByUser = pushTokenRepository.updateLastUsedByUser(anotherUser, LocalDateTime.now());
            int updatedByToken = pushTokenRepository.updateLastUsedByToken("nonexistent", LocalDateTime.now());

            // Then
            assertThat(deletedStale).isEqualTo(0);
            assertThat(updatedByUser).isEqualTo(0);
            assertThat(updatedByToken).isEqualTo(0);
        }

        @Test
        @DisplayName("Should handle very long token strings")
        void shouldHandleVeryLongTokenStrings() {
            // Given
            String longToken = "a".repeat(500); // Very long token
            PushToken tokenWithLongString = new PushToken();
            tokenWithLongString.setToken(longToken);
            tokenWithLongString.setUser(testUser);
            tokenWithLongString.setDeviceType(DeviceType.ANDROID);

            // When & Then
            assertThatCode(() -> {
                entityManager.persistAndFlush(tokenWithLongString);
            }).doesNotThrowAnyException();

            Optional<PushToken> foundToken = pushTokenRepository.findByToken(longToken);
            assertThat(foundToken).isPresent();
        }

        @Test
        @DisplayName("Should handle special characters in device ID")
        void shouldHandleSpecialCharactersInDeviceId() {
            // Given
            PushToken tokenWithSpecialChars = new PushToken();
            tokenWithSpecialChars.setToken("token-with-special-chars");
            tokenWithSpecialChars.setUser(testUser);
            tokenWithSpecialChars.setDeviceType(DeviceType.ANDROID);
            tokenWithSpecialChars.setDeviceId("device-123!@#$%^&*()");

            // When & Then
            assertThatCode(() -> {
                entityManager.persistAndFlush(tokenWithSpecialChars);
            }).doesNotThrowAnyException();

            List<PushToken> foundTokens = pushTokenRepository.findByDeviceId("device-123!@#$%^&*()");
            assertThat(foundTokens).hasSize(1);
        }

        @Test
        @DisplayName("Should handle null last used timestamp in stale token queries")
        void shouldHandleNullLastUsedTimestampInStaleTokenQueries() {
            // Given
            PushToken tokenWithNullLastUsed = new PushToken();
            tokenWithNullLastUsed.setToken("token-with-null-last-used");
            tokenWithNullLastUsed.setUser(anotherUser);
            tokenWithNullLastUsed.setDeviceType(DeviceType.IOS);
            tokenWithNullLastUsed.setLastUsed(null);

            entityManager.persistAndFlush(tokenWithNullLastUsed);
            LocalDateTime cutoffDate = LocalDateTime.now().minusDays(1);

            // When
            List<PushToken> staleTokens = pushTokenRepository.findStaleTokens(cutoffDate);

            // Then
            assertThat(staleTokens).hasSize(1);
            assertThat(staleTokens.get(0).getToken()).isEqualTo("token-with-null-last-used");
        }
    }
}
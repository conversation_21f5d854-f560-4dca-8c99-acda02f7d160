# Product Overview

This is an authentication and security microservice built for the GEDSYS platform. The service provides comprehensive user authentication, authorization, and session management capabilities.

## Core Features

- JWT-based authentication with access and refresh tokens
- Multi-session support (mobile and web) with different token expiration policies
- Push notification token management for mobile devices
- Role-based access control (USER, ADMIN)
- Secure password management with validation
- Session lifecycle management with proper cleanup
- PostgreSQL database with Flyway migrations

## Key Capabilities

- User registration and profile management
- Login/logout with session type awareness
- Token refresh with single-use enforcement
- Password change with session invalidation
- Admin user management with pagination
- Push token registration for mobile notifications
- Active session tracking and management

The service is designed as a standalone authentication provider that can be integrated with other microservices in the GEDSYS ecosystem.
# Project Structure

## Package Organization
The project follows standard Spring Boot conventions with a layered architecture under the base package `co.com.gedsys.authentication`.

### Main Source Structure
```
src/main/java/co/com/gedsys/authentication/
├── AuthenticationApplication.java          # Main Spring Boot application
├── config/                                 # Configuration classes
│   ├── JwtAuthenticationFilter.java        # JWT filter for request processing
│   ├── JwtProperties.java                  # JWT configuration properties
│   └── SecurityBeanConfig.java             # Security configuration
├── controller/                             # REST controllers
│   ├── AdminController.java                # Admin management endpoints
│   ├── AuthenticationController.java       # Auth endpoints (login, logout, etc.)
│   └── PushTokenController.java            # Push token management
├── dto/                                    # Data Transfer Objects
│   ├── *Request.java                       # Request DTOs
│   ├── *Response.java                      # Response DTOs
│   └── *Info.java                          # Information DTOs
├── entity/                                 # JPA entities
│   ├── User.java                           # User entity
│   ├── RefreshToken.java                   # Refresh token entity
│   ├── PushToken.java                      # Push token entity
│   └── *.java                              # Enums (Role, SessionType, DeviceType)
├── exception/                              # Custom exceptions
│   ├── *Exception.java                     # Domain-specific exceptions
├── repository/                             # Data access layer
│   └── *Repository.java                    # Spring Data JPA repositories
└── service/                                # Business logic layer
    ├── AuthenticationService.java          # Main auth orchestration
    ├── UserService.java                    # User management
    ├── JwtService.java                     # JWT operations
    ├── RefreshTokenService.java            # Token lifecycle
    └── PushTokenService.java               # Push notification tokens
```

### Resources Structure
```
src/main/resources/
├── application.properties                  # Main configuration
├── application-dev.properties              # Development profile
└── db/migration/                           # Flyway database migrations
    ├── V1__Create_users_table.sql
    ├── V2__Create_refresh_tokens_table.sql
    └── V3__Create_push_tokens_table.sql
```

### Test Structure
```
src/test/java/co/com/gedsys/authentication/
├── AuthenticationApplicationTests.java     # Main application test
├── BaseIntegrationTest.java               # Base class for integration tests
├── DatabaseIntegrationTest.java           # Database-specific tests
└── config/
    └── TestDatabaseConfig.java            # Test database configuration
```

## Architectural Patterns

### Layered Architecture
- **Controller Layer**: REST endpoints, request/response handling
- **Service Layer**: Business logic and orchestration
- **Repository Layer**: Data access abstraction
- **Entity Layer**: Domain models and database mapping

### Key Conventions
- Controllers handle HTTP concerns only, delegate to services
- Services contain business logic and coordinate between repositories
- DTOs for all external API contracts
- Entities are JPA-mapped domain objects
- Custom exceptions for domain-specific error handling
- Comprehensive logging with SLF4J
- Transaction management with `@Transactional`

### Database Design
- PostgreSQL with proper indexing strategies
- Flyway for version-controlled schema migrations
- Audit fields (created_at, updated_at) on entities
- Foreign key relationships with proper cascading
- Enum types stored as strings for readability

### Security Architecture
- JWT-based stateless authentication
- Role-based authorization with Spring Security
- Session type awareness (mobile vs web)
- Refresh token rotation for enhanced security
- Password validation and secure storage
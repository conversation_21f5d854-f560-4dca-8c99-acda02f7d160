# Design Document

## Overview

Este documento describe el diseño de un sistema de autenticación completo utilizando Spring Security 6.x con Spring Boot 3.5.4. El sistema implementará autenticación basada en JWT (JSON Web Tokens) con refresh tokens, gestión de usuarios y protección de endpoints.

La arquitectura seguirá los principios de Spring Security moderno, utilizando configuración basada en Java y aprovechando las características de seguridad más recientes disponibles en Spring Boot 3.x.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    Client[Cliente Web/Mobile] --> Controller[Authentication Controller]
    Controller --> Service[Authentication Service]
    Service --> UserService[User Service]
    Service --> JwtService[JWT Service]
    Service --> TokenService[Refresh Token Service]
    
    UserService --> UserRepo[User Repository]
    TokenService --> TokenRepo[Refresh Token Repository]
    Service --> PushTokenService[Push Token Service]
    
    UserRepo --> DB[(Base de Datos)]
    TokenRepo --> DB
    PushTokenService --> PushTokenRepo[Push Token Repository]
    PushTokenRepo --> DB
    
    JwtService --> JwtUtil[JWT Utilities]
    
    SecurityConfig[Security Configuration] --> JwtFilter[JWT Authentication Filter]
    JwtFilter --> JwtService
```

### Security Flow

```mermaid
sequenceDiagram
    participant C as Cliente
    participant AC as Auth Controller
    participant AS as Auth Service
    participant JS as JWT Service
    participant US as User Service
    participant DB as Database
    
    C->>AC: POST /auth/login (email, password)
    AC->>AS: authenticate(credentials)
    AS->>US: validateUser(email, password)
    US->>DB: findByEmail(email)
    DB-->>US: User entity
    US-->>AS: User validated
    AS->>JS: generateTokens(user)
    JS-->>AS: JWT + Refresh Token
    AS->>DB: saveRefreshToken(token)
    AS-->>AC: AuthResponse(jwt, refreshToken)
    AC-->>C: 200 OK + tokens
```

## Components and Interfaces

### 1. User Management

#### User Entity
```java
@Entity
@Table(name = "users", indexes = {
    @Index(name = "idx_users_email", columnList = "email"),
    @Index(name = "idx_users_enabled", columnList = "enabled")
})
public class User {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;
    
    @Column(name = "email", unique = true, nullable = false, length = 255)
    private String email;
    
    @Column(name = "password", nullable = false, length = 255)
    private String password;
    
    @Column(name = "first_name", length = 100)
    private String firstName;
    
    @Column(name = "last_name", length = 100)
    private String lastName;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "role", nullable = false, length = 50)
    private Role role = Role.USER;
    
    @Column(name = "created_at", nullable = false, updatable = false)
    @CreationTimestamp
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at", nullable = false)
    @UpdateTimestamp
    private LocalDateTime updatedAt;
    
    @Column(name = "enabled", nullable = false)
    private boolean enabled = true;
}
```

#### User Repository
```java
@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    Optional<User> findByEmail(String email);
    boolean existsByEmail(String email);
}
```

### 2. Authentication Components

#### JWT Service
- Generación y validación de JWT tokens
- Extracción de claims del token
- Configuración de expiración y firma

#### Refresh Token Service
- Gestión del ciclo de vida de refresh tokens con política de un solo uso
- Validación y renovación de tokens (invalidando el token usado)
- Limpieza de tokens expirados y usados
- Manejo de sesiones únicas por tipo (móvil/web)

#### Authentication Service
- Orquestación del proceso de autenticación
- Integración entre User Service y JWT Service
- Manejo de login, logout y refresh

#### Push Token Service
- Gestión de token único de notificaciones push por usuario
- Registro y reemplazo de token push en nuevas autenticaciones móviles
- Eliminación de token push en logout móvil
- Validación de que solo usuarios con sesión móvil tengan push token

### 3. Security Configuration

#### JWT Authentication Filter
```java
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {
    // Intercepta requests y valida JWT tokens
    // Establece el contexto de seguridad
}
```

#### Security Configuration
```java
@Configuration
@EnableWebSecurity
@EnableMethodSecurity
public class SecurityConfig {
    // Configuración de endpoints públicos/protegidos
    // Configuración del filtro JWT
    // Manejo de excepciones de seguridad
}
```

## Data Models

### User Profile Response
```java
public class UserProfileResponse {
    private Long id;
    private String email;
    private String firstName;
    private String lastName;
    private Role role;
    private LocalDateTime createdAt;
}
```

### Authentication Request/Response
```java
public class LoginRequest {
    @NotBlank
    @Email
    private String email;
    
    @NotBlank
    @Size(min = 6)
    private String password;
    
    @NotNull
    private SessionType sessionType; // MOBILE o WEB
    
    // Campos requeridos solo para sesiones móviles
    private String pushToken;
    private DeviceType deviceType;
    private String deviceId;
}

public class AuthResponse {
    private String accessToken;
    private String refreshToken;
    private String tokenType = "Bearer";
    private Long expiresIn;
    private UserProfileResponse user;
}

public class PushTokenRequest {
    @NotBlank
    private String pushToken;
    
    @NotNull
    private DeviceType deviceType;
    
    private String deviceId;
}

public enum DeviceType {
    ANDROID, IOS, WEB
}

public class ChangePasswordRequest {
    @NotBlank
    @Size(min = 6)
    private String currentPassword;
    
    @NotBlank
    @Size(min = 6)
    private String newPassword;
    
    @NotBlank
    @Size(min = 6)
    private String confirmNewPassword;
}

public class PagedResponse<T> {
    private List<T> content;
    private int page;
    private int size;
    private long totalElements;
    private int totalPages;
    private boolean first;
    private boolean last;
}

public class AdminUserResponse {
    private Long id;
    private String email;
    private String firstName;
    private String lastName;
    private Role role;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private boolean enabled;
    
    // Información de sesiones activas
    private List<ActiveSessionInfo> activeSessions;
    
    // Push token si existe
    private PushTokenInfo pushToken;
}

public class ActiveSessionInfo {
    private SessionType sessionType;
    private LocalDateTime lastActivity;
    private LocalDateTime expiresAt;
}

public class PushTokenInfo {
    private String deviceType;
    private String deviceId;
    private LocalDateTime createdAt;
    private LocalDateTime lastUsed;
}
```

### Refresh Token Entity
```java
@Entity
@Table(name = "refresh_tokens")
public class RefreshToken {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(unique = true, nullable = false)
    private String token;
    
    @ManyToOne
    @JoinColumn(name = "user_id")
    private User user;
    
    @Enumerated(EnumType.STRING)
    private SessionType sessionType; // MOBILE, WEB
    
    private LocalDateTime expiryDate;
    private LocalDateTime createdAt;
    private boolean used; // Para marcar tokens de un solo uso
    
    @Table(uniqueConstraints = {
        @UniqueConstraint(columnNames = {"user_id", "session_type"})
    })
}
```

### Push Token Entity
```java
@Entity
@Table(name = "push_tokens")
public class PushToken {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false)
    private String token;
    
    @OneToOne
    @JoinColumn(name = "user_id")
    private User user; // Un usuario solo puede tener un push token
    
    @Enumerated(EnumType.STRING)
    private DeviceType deviceType; // ANDROID, IOS
    
    private String deviceId;
    private LocalDateTime createdAt;
    private LocalDateTime lastUsed;
    
    @Table(uniqueConstraints = {
        @UniqueConstraint(columnNames = {"user_id"}) // Solo un token por usuario
    })
}

public enum SessionType {
    MOBILE, WEB
}
```

## Error Handling

### Custom Exception Classes
- `InvalidCredentialsException`: Credenciales inválidas
- `TokenExpiredException`: Token expirado
- `InvalidTokenException`: Token malformado o inválido
- `UserNotFoundException`: Usuario no encontrado
- `EmailAlreadyExistsException`: Email ya registrado

### Global Exception Handler
```java
@RestControllerAdvice
public class AuthenticationExceptionHandler {
    // Manejo centralizado de excepciones de autenticación
    // Respuestas HTTP consistentes
    // Logging de eventos de seguridad
}
```

### Error Response Format
```java
public class ErrorResponse {
    private String error;
    private String message;
    private int status;
    private LocalDateTime timestamp;
    private String path;
}
```

## Testing Strategy

### Unit Tests
- **Service Layer**: Pruebas unitarias para AuthenticationService, UserService, JwtService
- **Repository Layer**: Pruebas de repositorios con @DataJpaTest
- **Utility Classes**: Pruebas para JWT utilities y validaciones

### Integration Tests
- **Controller Tests**: Pruebas de endpoints con @WebMvcTest
- **Security Tests**: Pruebas de configuración de seguridad con @SpringBootTest
- **Database Tests**: Pruebas de persistencia con TestContainers
- **Pagination Tests**: Pruebas de paginación en endpoints de administración
- **Password Change Tests**: Pruebas de validación de cambio de contraseña

### Security Tests
- **Authentication Flow**: Pruebas completas del flujo de login/logout
- **Token Validation**: Pruebas de validación de JWT y refresh tokens
- **Authorization**: Pruebas de acceso a endpoints protegidos
- **Error Scenarios**: Pruebas de manejo de errores y casos edge

### Test Data Management
- Uso de @Sql para datos de prueba
- Factories para creación de entidades de prueba
- Mocking de servicios externos
- Spring Docker Compose plugin para PostgreSQL 17 en tests de integración

#### Test Database Configuration with Docker Compose

**compose-test.yaml**
```yaml
services:
  postgres-test:
    image: postgres:17
    environment:
      POSTGRES_DB: test_auth_db
      POSTGRES_USER: test_user
      POSTGRES_PASSWORD: test_password
    ports:
      - "5433:5432"
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U test_user -d test_auth_db"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres_test_data:
```

#### Test Configuration
```java
@TestConfiguration
@TestPropertySource(properties = {
    "spring.docker.compose.file=compose-test.yaml",
    "spring.docker.compose.lifecycle-management=start_and_stop",
    "spring.docker.compose.start.command=up",
    "spring.docker.compose.stop.command=down",
    "spring.docker.compose.stop.timeout=1m"
})
public class TestDatabaseConfig {
    
    @Bean
    @Primary
    @TestScope
    public DataSource testDataSource() {
        // Spring Docker Compose plugin will automatically configure the DataSource
        // based on the running PostgreSQL container
        return DataSourceBuilder.create()
                .url("*********************************************")
                .username("test_user")
                .password("test_password")
                .driverClassName("org.postgresql.Driver")
                .build();
    }
}
```

#### Test Dependencies
```xml
<!-- Spring Docker Compose Support -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-docker-compose</artifactId>
    <scope>test</scope>
</dependency>

<!-- PostgreSQL Driver for tests -->
<dependency>
    <groupId>org.postgresql</groupId>
    <artifactId>postgresql</artifactId>
    <scope>test</scope>
</dependency>
```

#### Integration Test Base Class
```java
@SpringBootTest
@TestPropertySource(properties = {
    "spring.docker.compose.file=compose-test.yaml",
    "spring.datasource.url=*********************************************",
    "spring.datasource.username=test_user",
    "spring.datasource.password=test_password"
})
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
public abstract class BaseIntegrationTest {
    
    @Autowired
    protected TestRestTemplate restTemplate;
    
    @Autowired
    protected DataSource dataSource;
    
    @BeforeEach
    void setUp() {
        // Database is automatically started by Spring Docker Compose plugin
        // Flyway migrations are applied automatically
    }
    
    @AfterEach
    void tearDown() {
        // Clean up test data if needed
        // Container will be stopped automatically after test class
    }
}

## Configuration

### JWT Configuration
```properties
# JWT Settings - Configurables
app.jwt.secret=${JWT_SECRET:your-secret-key-here}
app.jwt.access-token-expiration=${JWT_ACCESS_EXPIRATION:3600000}  # 1 hour default
app.jwt.refresh-token-expiration=${JWT_REFRESH_EXPIRATION:604800000}  # 7 days default

# Configuración específica por tipo de sesión
app.jwt.mobile.access-token-expiration=${JWT_MOBILE_ACCESS_EXPIRATION:7200000}  # 2 hours for mobile
app.jwt.web.access-token-expiration=${JWT_WEB_ACCESS_EXPIRATION:3600000}  # 1 hour for web
```

### JWT Configuration Class
```java
@ConfigurationProperties(prefix = "app.jwt")
@Component
public class JwtProperties {
    private String secret;
    private long accessTokenExpiration;
    private long refreshTokenExpiration;
    
    private Mobile mobile = new Mobile();
    private Web web = new Web();
    
    @Data
    public static class Mobile {
        private long accessTokenExpiration = 7200000; // 2 hours default
    }
    
    @Data
    public static class Web {
        private long accessTokenExpiration = 3600000; // 1 hour default
    }
}
```

### Database Configuration

#### PostgreSQL 17 Configuration
```properties
# Database Configuration
spring.datasource.url=**************************************************
spring.datasource.username=${DB_USERNAME:auth_user}
spring.datasource.password=${DB_PASSWORD:auth_password}
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA/Hibernate Configuration
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.jdbc.time_zone=UTC

# Connection Pool Configuration (HikariCP)
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.connection-timeout=20000
spring.datasource.hikari.max-lifetime=1200000

# Flyway Configuration
spring.flyway.enabled=true
spring.flyway.locations=classpath:db/migration
spring.flyway.baseline-on-migrate=true
spring.flyway.validate-on-migrate=true
```

#### Required Dependencies
```xml
<!-- PostgreSQL Driver -->
<dependency>
    <groupId>org.postgresql</groupId>
    <artifactId>postgresql</artifactId>
    <scope>runtime</scope>
</dependency>

<!-- Flyway Core -->
<dependency>
    <groupId>org.flywaydb</groupId>
    <artifactId>flyway-core</artifactId>
</dependency>

<!-- Flyway PostgreSQL -->
<dependency>
    <groupId>org.flywaydb</groupId>
    <artifactId>flyway-database-postgresql</artifactId>
</dependency>
```

#### Flyway Maven Plugin
```xml
<plugin>
    <groupId>org.flywaydb</groupId>
    <artifactId>flyway-maven-plugin</artifactId>
    <version>10.21.0</version>
    <configuration>
        <url>**************************************************</url>
        <user>${DB_USERNAME}</user>
        <password>${DB_PASSWORD}</password>
        <locations>
            <location>classpath:db/migration</location>
        </locations>
        <baselineOnMigrate>true</baselineOnMigrate>
        <validateOnMigrate>true</validateOnMigrate>
    </configuration>
    <dependencies>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>42.7.4</version>
        </dependency>
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-database-postgresql</artifactId>
            <version>10.21.0</version>
        </dependency>
    </dependencies>
</plugin>
```

#### Flyway Commands
```bash
# Migrate database
mvn flyway:migrate

# Clean database (development only)
mvn flyway:clean

# Info about migrations
mvn flyway:info

# Validate migrations
mvn flyway:validate
```

#### Flyway Migration Scripts Structure
```
src/main/resources/db/migration/
├── V1__Create_users_table.sql
├── V2__Create_refresh_tokens_table.sql
├── V3__Create_push_tokens_table.sql
├── V4__Add_indexes.sql
└── V5__Insert_default_admin_user.sql
```

#### Sample Migration Scripts

**V1__Create_users_table.sql**
```sql
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    email VARCHAR(255) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    role VARCHAR(50) NOT NULL DEFAULT 'USER',
    enabled BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_enabled ON users(enabled);
```

**V2__Create_refresh_tokens_table.sql**
```sql
CREATE TABLE refresh_tokens (
    id BIGSERIAL PRIMARY KEY,
    token VARCHAR(255) NOT NULL UNIQUE,
    user_id BIGINT NOT NULL,
    session_type VARCHAR(20) NOT NULL,
    expiry_date TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    used BOOLEAN NOT NULL DEFAULT false,
    
    CONSTRAINT fk_refresh_tokens_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT uk_user_session_type UNIQUE (user_id, session_type)
);

CREATE INDEX idx_refresh_tokens_user_id ON refresh_tokens(user_id);
CREATE INDEX idx_refresh_tokens_expiry ON refresh_tokens(expiry_date);
CREATE INDEX idx_refresh_tokens_used ON refresh_tokens(used);
```

**V3__Create_push_tokens_table.sql**
```sql
CREATE TABLE push_tokens (
    id BIGSERIAL PRIMARY KEY,
    token VARCHAR(500) NOT NULL,
    user_id BIGINT NOT NULL UNIQUE,
    device_type VARCHAR(20) NOT NULL,
    device_id VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_used TIMESTAMP WITH TIME ZONE,
    
    CONSTRAINT fk_push_tokens_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

CREATE INDEX idx_push_tokens_user_id ON push_tokens(user_id);
CREATE INDEX idx_push_tokens_device_type ON push_tokens(device_type);
```

### Security Headers
- CORS configuration
- CSRF protection (disabled for stateless JWT)
- Security headers (X-Frame-Options, etc.)

## Performance Considerations

### Token Storage
- Refresh tokens almacenados en base de datos
- JWT tokens stateless (no almacenados en servidor)
- Índices en tablas de usuarios y refresh tokens

### Caching Strategy
- Cache de usuarios frecuentemente accedidos
- Cache de configuración JWT
- Limpieza periódica de tokens expirados

### Session Management Strategy

#### Single Session Policy
- Un usuario puede tener máximo una sesión móvil activa y una sesión web activa simultáneamente
- Al autenticarse en un nuevo dispositivo del mismo tipo, la sesión anterior se invalida automáticamente
- Los refresh tokens son de un solo uso y se regeneran en cada renovación
- Push tokens son únicos por usuario y se eliminan en logout móvil

#### Token Lifecycle
```mermaid
sequenceDiagram
    participant U as Usuario
    participant S as Sistema
    participant DB as Database
    
    U->>S: Login (sessionType: MOBILE)
    S->>DB: Invalidar refresh token móvil existente
    S->>DB: Crear nuevo refresh token móvil
    S->>DB: Reemplazar push token existente
    S-->>U: JWT + Refresh Token + Push Token guardado
    
    Note over S,DB: Refresh Token Usage (One-time use)
    U->>S: Refresh Token
    S->>DB: Marcar refresh token como usado
    S->>DB: Crear nuevo refresh token
    S-->>U: Nuevo JWT + Nuevo Refresh Token
```

### Security Best Practices
- Refresh tokens de un solo uso con regeneración automática
- Invalidación automática de sesiones anteriores del mismo tipo
- Configuración parametrizable de tiempos de expiración
- Limpieza automática de tokens expirados y usados
- Rate limiting en endpoints de autenticación
- Logging de eventos de seguridad y cambios de sesión

## API Endpoints

### Authentication Endpoints
- `POST /auth/login` - Autenticación con credenciales (incluye push token opcional)
- `POST /auth/refresh` - Renovación de tokens JWT
- `POST /auth/logout` - Cierre de sesión
- `GET /auth/profile` - Obtener perfil del usuario autenticado
- `PUT /auth/change-password` - Cambiar contraseña del usuario autenticado

### Push Token Management
- `POST /auth/push-token` - Registrar/actualizar token de notificación push (solo sesiones móviles)
- `DELETE /auth/push-token` - Eliminar token de push del usuario autenticado
- `GET /auth/push-token` - Obtener token de push del usuario autenticado

### User Management (Admin)
- `POST /admin/users` - Crear nuevo usuario
- `PUT /admin/users/{id}` - Actualizar usuario
- `DELETE /admin/users/{id}` - Eliminar usuario
- `GET /admin/users` - Listar usuarios con paginación
  - Query params: `page` (default: 0), `size` (default: 20), `sort` (default: "createdAt,desc")
  - Filtros opcionales: `email`, `enabled`, `role`
- `GET /admin/users/{id}` - Obtener usuario por ID con todos los datos (incluye pushToken y sesiones activas)
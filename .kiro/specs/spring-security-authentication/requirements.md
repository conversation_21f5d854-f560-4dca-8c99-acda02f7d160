# Requirements Document

## Introduction

Este documento define los requerimientos para implementar un sistema de autenticación completo utilizando Spring Security. El sistema permitirá la creación y administración de cuentas de usuarios, autenticación mediante usuario y contraseña con tokens JWT, y manejo de refresh tokens para mantener sesiones seguras y persistentes.

## Requirements

### Requirement 1

**User Story:** Como administrador del sistema, quiero poder gestionar cuentas de usuarios, para que pueda controlar el acceso al sistema y mantener la seguridad.

#### Acceptance Criteria

1. WHEN un administrador crea una nueva cuenta THEN el sistema SHALL validar que el email sea único
2. WHEN un administrador crea una nueva cuenta THEN el sistema SHALL encriptar la contraseña antes de almacenarla
3. WHEN un administrador actualiza una cuenta THEN el sistema SHALL mantener la integridad de los datos existentes
4. WHEN un administrador elimina una cuenta THEN el sistema SHALL invalidar todos los tokens asociados
5. IF un usuario intenta registrarse con un email existente THEN el sistema SHALL retornar un error de conflicto

### Requirement 2

**User Story:** Como usuario del sistema, quiero poder autenticarme con mi email y contraseña, para que pueda acceder de forma segura a los recursos protegidos.

#### Acceptance Criteria

1. WHEN un usuario envía credenciales válidas THEN el sistema SHALL generar un JWT token de acceso
2. WHEN un usuario envía credenciales válidas THEN el sistema SHALL generar un refresh token
3. WHEN un usuario envía credenciales inválidas THEN el sistema SHALL retornar un error de autenticación
4. WHEN un usuario se autentica exitosamente THEN el sistema SHALL retornar ambos tokens en la respuesta
5. IF las credenciales están vacías o mal formateadas THEN el sistema SHALL retornar un error de validación

### Requirement 3

**User Story:** Como usuario autenticado, quiero que mi sesión se mantenga activa de forma segura con política de sesión única, para que tenga control sobre mis sesiones activas y mantenga la seguridad.

#### Acceptance Criteria

1. WHEN un JWT token expira THEN el usuario SHALL poder usar el refresh token para obtener uno nuevo
2. WHEN un refresh token es usado THEN el sistema SHALL generar un nuevo refresh token e invalidar el anterior
3. WHEN un refresh token expira THEN el usuario SHALL necesitar autenticarse nuevamente
4. WHEN un usuario hace logout THEN el sistema SHALL invalidar tanto el JWT como el refresh token
5. WHEN un usuario se autentica en un nuevo dispositivo del mismo tipo THEN el sistema SHALL invalidar la sesión anterior del mismo tipo
6. IF un usuario tiene sesión móvil activa y se autentica en web THEN ambas sesiones SHALL permanecer activas
7. IF un usuario tiene sesión web activa y se autentica en móvil THEN ambas sesiones SHALL permanecer activas
8. WHEN un usuario se autentica en móvil teniendo ya sesión móvil THEN la sesión móvil anterior SHALL ser invalidada

### Requirement 4

**User Story:** Como desarrollador del sistema, quiero que los endpoints estén protegidos adecuadamente, para que solo usuarios autenticados puedan acceder a recursos sensibles.

#### Acceptance Criteria

1. WHEN un usuario accede a un endpoint protegido sin token THEN el sistema SHALL retornar error 401
2. WHEN un usuario accede con un token válido THEN el sistema SHALL permitir el acceso
3. WHEN un token JWT expira THEN el sistema SHALL retornar error 401 con mensaje específico
4. WHEN se configura la seguridad THEN el sistema SHALL permitir acceso público a endpoints de autenticación
5. IF un token está malformado THEN el sistema SHALL retornar error 400 con mensaje descriptivo

### Requirement 5

**User Story:** Como usuario autenticado, quiero poder validar mi token y obtener mi información de perfil, para que el sistema pueda verificar mi identidad y mostrar mis datos personales.

#### Acceptance Criteria

1. WHEN un usuario envía un token JWT válido THEN el sistema SHALL validar la firma y expiración del token
2. WHEN un token es válido THEN el sistema SHALL extraer la información del usuario del token
3. WHEN se valida un token exitosamente THEN el sistema SHALL retornar el perfil completo del usuario
4. WHEN un token es inválido o expirado THEN el sistema SHALL retornar error de autorización
5. IF el usuario del token no existe en la base de datos THEN el sistema SHALL retornar error de usuario no encontrado

### Requirement 6

**User Story:** Como usuario de aplicación móvil, quiero que el sistema gestione mi token de notificaciones push de forma segura con política de sesión única, para que reciba notificaciones solo en mi dispositivo activo.

#### Acceptance Criteria

1. WHEN un usuario móvil se autentica THEN el sistema SHALL permitir el registro de un token de notificación push
2. WHEN se registra un token push THEN el sistema SHALL reemplazar cualquier token push anterior del usuario
3. WHEN un usuario hace logout desde móvil THEN el sistema SHALL eliminar el token push asociado
4. WHEN un usuario se autentica en un nuevo dispositivo móvil THEN el sistema SHALL invalidar el token push del dispositivo anterior
5. IF un usuario solo tiene sesión web activa THEN no SHALL tener token push registrado

### Requirement 7

**User Story:** Como administrador del sistema, quiero poder configurar los tiempos de validez de los tokens JWT, para que pueda ajustar la seguridad según las necesidades del negocio.

#### Acceptance Criteria

1. WHEN se configura el tiempo de expiración de JWT THEN el sistema SHALL usar ese valor para generar tokens
2. WHEN se configura el tiempo de expiración de refresh tokens THEN el sistema SHALL aplicar esa configuración
3. WHEN se cambia la configuración THEN los nuevos tokens SHALL usar los nuevos tiempos de expiración
4. WHEN no se especifica configuración THEN el sistema SHALL usar valores por defecto seguros
5. IF la configuración es inválida THEN el sistema SHALL usar valores por defecto y loggear el error

### Requirement 8

**User Story:** Como usuario autenticado, quiero poder cambiar mi contraseña de forma segura, para que pueda mantener la seguridad de mi cuenta.

#### Acceptance Criteria

1. WHEN un usuario solicita cambio de contraseña THEN el sistema SHALL validar la contraseña actual
2. WHEN se valida la contraseña actual THEN el sistema SHALL verificar que la nueva contraseña sea diferente
3. WHEN se proporciona nueva contraseña THEN el sistema SHALL validar que coincida con la confirmación
4. WHEN se cambia la contraseña exitosamente THEN el sistema SHALL invalidar todas las sesiones activas del usuario
5. IF la contraseña actual es incorrecta THEN el sistema SHALL retornar error de validación

### Requirement 9

**User Story:** Como administrador del sistema, quiero poder consultar usuarios con paginación y obtener información detallada, para que pueda gestionar eficientemente grandes volúmenes de usuarios.

#### Acceptance Criteria

1. WHEN se consulta la lista de usuarios THEN el sistema SHALL retornar resultados paginados
2. WHEN se especifican parámetros de paginación THEN el sistema SHALL aplicar page, size y sort correctamente
3. WHEN se consulta un usuario por ID THEN el sistema SHALL retornar todos los datos incluyendo pushToken
4. WHEN se consulta un usuario por ID THEN el sistema SHALL incluir información de sesiones activas
5. IF se especifican filtros THEN el sistema SHALL aplicarlos a la consulta paginada

### Requirement 10

**User Story:** Como administrador de seguridad, quiero que el sistema maneje errores de autenticación de forma consistente, para que pueda monitorear y responder a intentos de acceso no autorizados.

#### Acceptance Criteria

1. WHEN ocurre un error de autenticación THEN el sistema SHALL loggear el evento con detalles relevantes
2. WHEN se intenta acceso no autorizado THEN el sistema SHALL retornar respuestas HTTP estándar
3. WHEN hay múltiples intentos fallidos THEN el sistema SHALL registrar la actividad sospechosa
4. WHEN se manejan excepciones de seguridad THEN el sistema SHALL no exponer información sensible
5. IF hay errores internos THEN el sistema SHALL retornar mensajes genéricos al cliente

### Requirement 11

**User Story:** Como desarrollador del sistema, quiero que las pruebas utilicen una base de datos PostgreSQL en contenedor que se lance automáticamente, para que pueda ejecutar tests de integración de forma consistente y aislada sin configuración manual.

#### Acceptance Criteria

1. WHEN se ejecutan las pruebas THEN el sistema SHALL iniciar automáticamente un contenedor PostgreSQL usando Spring Docker Compose plugin
2. WHEN se inicia el contenedor de pruebas THEN el sistema SHALL usar PostgreSQL 17 como versión de base de datos
3. WHEN se ejecutan múltiples suites de pruebas THEN cada ejecución SHALL usar una instancia limpia de base de datos
4. WHEN las pruebas terminan THEN el sistema SHALL limpiar automáticamente el contenedor de base de datos
5. IF el contenedor no puede iniciarse THEN el sistema SHALL fallar las pruebas con mensaje descriptivo
6. WHEN se configura el entorno de pruebas THEN el sistema SHALL aplicar las migraciones Flyway automáticamente al contenedor